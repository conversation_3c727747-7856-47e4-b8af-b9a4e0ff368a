import type { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  // First, rename the table to a more generic name
  await knex.schema.renameTable(TABLE.DOCTOR_EMPLOYEES, "user_employees");
  
  // Add a column to specify the type of supervisor (doctor or specialist) 
  await knex.schema.alterTable("user_employees", (table) => {
    table.enum("supervisor_type", ["doctor", "specialist"]).notNullable().defaultTo("doctor");
  });
  
  // Rename the column first
  await knex.schema.alterTable("user_employees", (table) => {
    table.renameColumn("doctor_id", "supervisor_id");
  });
  
  // Drop existing constraints using raw SQL to handle naming issues
  await knex.raw(`
    ALTER TABLE user_employees 
    DROP CONSTRAINT IF EXISTS doctor_employees_doctor_id_foreign;
  `);
  
  await knex.raw(`
    ALTER TABLE user_employees 
    DROP CONSTRAINT IF EXISTS doctor_employees_doctor_id_employee_id_unique;
  `);
  
  // Add new foreign key constraint
  await knex.schema.alterTable("user_employees", (table) => {
    table.foreign("supervisor_id").references("id").inTable(TABLE.USERS);
  });
  
  // Add new unique constraint
  await knex.schema.alterTable("user_employees", (table) => {
    table.unique(["supervisor_id", "employee_id", "supervisor_type"]);
  });
}

export async function down(knex: Knex): Promise<void> {
  // Drop the new constraints
  await knex.raw(`
    ALTER TABLE user_employees 
    DROP CONSTRAINT IF EXISTS user_employees_supervisor_id_employee_id_supervisor_type_unique;
  `);
  
  await knex.raw(`
    ALTER TABLE user_employees 
    DROP CONSTRAINT IF EXISTS user_employees_supervisor_id_foreign;
  `);
  
  // Rename column back
  await knex.schema.alterTable("user_employees", (table) => {
    table.renameColumn("supervisor_id", "doctor_id");
    table.dropColumn("supervisor_type");
  });
  
  // Add back the original constraints
  await knex.schema.alterTable("user_employees", (table) => {
    table.foreign("doctor_id").references("id").inTable(TABLE.USERS);
    table.unique(["doctor_id", "employee_id"]);
  });
  
  // Rename table back
  await knex.schema.renameTable("user_employees", TABLE.DOCTOR_EMPLOYEES);
}