import { Request, Response } from "express";
import morgan from "morgan";
import { accessLogger } from "./logger";

export const morganConfig = (app: any) => {
  // Custom tokens
  morgan.token("host", function (req: Request, res: Response) {
    return req.hostname;
  });
  
  morgan.token("user-id", function (req: Request, res: Response) {
    return (req as any).user?.id || 'anonymous';
  });
  
  morgan.token("real-ip", function (req: Request, res: Response) {
    return req.headers['x-forwarded-for'] as string || req.ip || req.connection?.remoteAddress || 'unknown';
  });

  // Custom format function for structured logging
  const customFormat = (tokens: any, req: Request, res: Response) => {
    const logData = {
      method: tokens.method(req, res),
      url: tokens.url(req, res),
      status: parseInt(tokens.status(req, res)),
      contentLength: tokens.res(req, res, 'content-length') || '0',
      responseTime: parseFloat(tokens['response-time'](req, res)),
      userAgent: tokens['user-agent'](req, res),
      host: tokens.host(req, res),
      ip: tokens['real-ip'](req, res),
      userId: tokens['user-id'](req, res),
      referrer: tokens.referrer(req, res) || '',
      timestamp: new Date().toISOString()
    };
    
    // Log to access logger
    accessLogger.info('HTTP Request', logData);
    
    return JSON.stringify(logData);
  };

  app.use(morgan(customFormat));
};
