import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // 1. Drop existing check constraint
  await knex.raw(`
    ALTER TABLE patient_versions
    DROP CONSTRAINT IF EXISTS patient_versions_status_check;
  `);

  // 2. Add new check constraint with extra value
  await knex.raw(`
    ALTER TABLE patient_versions
    ADD CONSTRAINT patient_versions_status_check
    CHECK (
      status IN (
        'sent_by_doctor',
        'approved_by_specialist',
        'rejected_by_specialist',
        'sent_by_specialist',
        'approved_by_doctor',
        'rejected_by_doctor',
        'accepted',
        'in_working',
        'completed',
        'cancelled_by_admin'
      )
    );
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Revert back to old constraint (without cancelled_by_admin)
  await knex.raw(`
    ALTER TABLE patient_versions
    DROP CONSTRAINT IF EXISTS patient_versions_status_check;
  `);

  await knex.raw(`
    ALTER TABLE patient_versions
    ADD CONSTRAINT patient_versions_status_check
    CHECK (
      status IN (
        'sent_by_doctor',
        'approved_by_specialist',
        'rejected_by_specialist',
        'sent_by_specialist',
        'approved_by_doctor',
        'rejected_by_doctor',
        'accepted',
        'in_working',
        'completed'
      )
    );
  `);
}
