// socket.ts
import { Server as HttpServer } from "http";
import { Server as SocketServer, Socket } from "socket.io";
import { setupChat } from "../controller/chat.controller";
import { fetchNotifications } from "../utils/helperFunctions";
import { appLogger, errorLogger } from "./logger";

let io: SocketServer;
export let users: {
  userId: string;
  socketIds: string[];
}[] = [];

export const initSocket = (server: HttpServer) => {
  // Track connections per IP to prevent connection flooding
  const connectionTracker = new Map<
    string,
    { count: number; lastConnection: number }
  >();

  io = new SocketServer(server, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"],
      credentials: true,
    },
    maxHttpBufferSize: 100 * 1024 * 1024,
    transports: ["websocket", "polling"],
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 30000,
    allowEIO3: true,
    // Add connection limits
    connectionStateRecovery: {
      maxDisconnectionDuration: 2 * 60 * 1000,
      skipMiddlewares: true,
    },
  });

  io.on("connection", (socket: Socket) => {
    const clientIp = socket.handshake.address;
    const userAgent = socket.handshake.headers["user-agent"];

    // Check for connection flooding
    const now = Date.now();
    const ipData = connectionTracker.get(clientIp) || {
      count: 0,
      lastConnection: 0,
    };

    // Reset count if last connection was more than 5 seconds ago
    if (now - ipData.lastConnection > 5000) {
      ipData.count = 0;
    }

    ipData.count++;
    ipData.lastConnection = now;
    connectionTracker.set(clientIp, ipData);

    // If too many connections from same IP in short time, disconnect
    if (ipData.count > 5) {
      appLogger.warn("Connection flooding detected", {
        clientIp,
        count: ipData.count,
        socketId: socket.id,
      });
      socket.emit(
        "error",
        "Too many connection attempts. Please wait before reconnecting."
      );
      socket.disconnect(true);
      return;
    }

    appLogger.info("Socket connection established", {
      socketId: socket.id,
      clientIp,
      userAgent,
      timestamp: new Date().toISOString(),
      transport: socket.conn.transport.name,
    });

    // Handle transport upgrade
    socket.conn.on("upgrade", () => {
      appLogger.info("Socket transport upgraded", {
        socketId: socket.id,
        transport: socket.conn.transport.name,
        clientIp,
      });
    });

    // Handle transport upgrade error
    socket.conn.on("upgradeError", (error) => {
      errorLogger.error("Socket transport upgrade error", {
        socketId: socket.id,
        error: error.message,
        clientIp,
      });
    });

    socket.on("join", (userId: string) => {
      try {
        appLogger.info("User joined socket room", {
          userId,
          socketId: socket.id,
          clientIp,
        });

        const existingUser = users.find((user) => user.userId === userId);
        if (existingUser) {
          if (!existingUser.socketIds.includes(socket.id)) {
            existingUser.socketIds.push(socket.id);
          }
        } else {
          users.push({ userId, socketIds: [socket.id] });
        }

        socket.join(`user_${userId}`);
      } catch (error: any) {
        errorLogger.error("Socket join error", {
          error: error.message,
          userId,
          socketId: socket.id,
          clientIp,
        });
      }
    });

    socket.on("disconnect", (reason) => {
      appLogger.info("Socket disconnected", {
        socketId: socket.id,
        reason,
        clientIp,
        timestamp: new Date().toISOString(),
      });

      // Clean up connection tracker
      const ipData = connectionTracker.get(clientIp);
      if (ipData) {
        ipData.count = Math.max(0, ipData.count - 1);
        if (ipData.count === 0) {
          connectionTracker.delete(clientIp);
        } else {
          connectionTracker.set(clientIp, ipData);
        }
      }

      users = users
        .map((user) => ({
          ...user,
          socketIds: user.socketIds.filter((id) => id !== socket.id),
        }))
        .filter((user) => user.socketIds.length > 0);
    });

    socket.on("error", (error) => {
      errorLogger.error("Socket error", {
        error: error.message,
        socketId: socket.id,
        clientIp,
      });
    });

    setupChat(io, socket);
  });

  // Periodic cleanup of connection tracker (every 30 seconds)
  setInterval(() => {
    const now = Date.now();
    for (const [ip, data] of connectionTracker.entries()) {
      if (now - data.lastConnection > 30000) {
        connectionTracker.delete(ip);
      }
    }
  }, 30000);

  appLogger.info("Socket.IO server initialized successfully");
  return io;
};

export const getIO = () => {
  if (!io) {
    throw new Error("Socket.IO not initialized");
  }
  return io;
};
