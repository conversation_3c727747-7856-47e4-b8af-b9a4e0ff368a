import { Router } from "express";
import AUTHROUT<PERSON> from "./auth.routes";
import DOCTORROUTES from "./doctor.routes";
import PERMISSIONROUTES from "./permission.routes";
import SPECIA<PERSON>ISTROUTES from "./specialist.routes";
import ADMINPLA<PERSON>OUTES from "./admin/plan.routes";
import USERROUTES from "./admin/user.routes";
import { authAdminMiddleware } from "../middlewares/authAdminMiddleware";
import ADMINDASHBOARDROUTES from "./admin/dashboard.routes";
import ADMINUSEROUTES from "./admin/user.routes";
import OSSRoutes from "./oss.routes";
import SUPPORTROUTES from "./support.routes";
const router = Router();

router.use("/auth", AUTHROUTES);
router.use("/admin", ADMINPLANROUTES);
router.use("/doctor", DOCTORROUTES);
router.use("/permissions", PERMISSIONROUTES);
router.use("/specialist", SPECIALISTROUTES);
router.use("/user", authAdminMiddleware, USERROUTES);
router.use("/admin", ADMIND<PERSON><PERSON>BOARDROUTES);
router.use("/file", OSSRoutes);
router.use("/admin", ADMINUSEROUTES);
router.use("/support", SUPPORTROUTES);



export default router;
