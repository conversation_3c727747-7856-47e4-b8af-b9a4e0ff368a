import { z } from "zod";

export const createUserSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email format"),
  username: z.string().optional(),
  password: z.string().optional(),
  role_id: z.number().optional().or(z.string().transform(val => parseInt(val, 10))),
});

export const updateUserSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().email("Invalid email format").optional(),
  username: z.string().optional(),
  role_id: z.number().optional().or(z.string().transform(val => parseInt(val, 10))),
});

export const createEmployeeSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email format"),
  username: z.string().optional(),
  password: z.string().optional(),
  salutation: z.string().optional(),
  practice_phone_number: z.string().min(1, "Practice phone number is required"),
  mobile: z.string().optional(),
  profession: z.string().optional(),
});

export const createEmployeeUpdateSchema = z.object({
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  email: z.string().email("Invalid email format").optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  salutation: z.string().optional(),
  practice_phone_number: z.string().optional(),
  mobile: z.string().optional(),
  profession: z.string().optional(),
});
