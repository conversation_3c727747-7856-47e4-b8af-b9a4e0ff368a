import type { Request, Response } from "express";
import as<PERSON><PERSON><PERSON><PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import { sendSupportEmail } from "../../utils/services/nodemailer/doctorCredential";
import validate from "../../validations";
import { supportSchema } from "../../validations/supportSchema";

export const sendMailToSupport = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const validationResult = validate(supportSchema, req.body, res);
      if (!validationResult.success) {
        return; 
      }
      const { name, message, email, phone_number } = validationResult.data;

      const [newSupportRequest] = await db(TABLE.SUPPORT)
        .insert({
          name,
          message,
          email,
          phone_number,
          created_at: new Date(),
        })
        .returning("*");

      if (!newSupportRequest) {
        return sendResponse(res, 500, "Failed to save message to database", false);
      }

      try {
        await sendSupportEmail({ name, email, phone_number, message });
        return sendResponse(
          res,
          200,
          "Support request created and email sent successfully",
          true,
          newSupportRequest
        );
      } catch (emailError: any) {
        console.error("Email sending failed:", emailError);
        return sendResponse(
          res,
          200,
          "Support request created, but email sending failed",
          true,
          newSupportRequest
        );
      }
    } catch (error: any) {
      console.error("sendMailToSupport error:", error);
      const errorMessage =
        error.code === "ER_DUP_ENTRY"
          ? "Duplicate entry detected"
          : error.code === "ER_NO_SUCH_TABLE"
          ? "Support table does not exist"
          : error.message || "Something went wrong";
      return sendResponse(res, 500, errorMessage, false);
    }
  }
);

export const getAllSupportTickets = asyncHandler(
  async (req: Request, res: Response) => {  
    try {
      const supportTickets = await db(TABLE.SUPPORT).select("*");
      if (supportTickets.length === 0) {
        return sendResponse(res, 404, "No support tickets found", false);
      }
      sendResponse(res, 200, "Support tickets retrieved successfully", true, supportTickets);
    } catch (error) {
      console.error("Error retrieving support tickets:", error);
      sendResponse(res, 500, "Internal server error", false);
    }
  } 
);
