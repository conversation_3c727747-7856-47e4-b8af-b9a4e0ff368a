import type { Request, Response } from "express";
import as<PERSON><PERSON><PERSON><PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import {
  uploadToOSS,
  deleteFromOSS,
  getSignedUrl,
} from "../../utils/services/oss/ossHelper";

export const updateProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;

    const user = await db(TABLE.USERS)
      .where({ id: userId, is_deleted: false })
      .first();

    if (!user) {
      return sendResponse(res, 404, "User not found", false);
    }

    const { first_name, last_name, email, username } = req.body;

    // Email uniqueness check
    if (email) {
      const emailExists = await db(TABLE.USERS)
        .where({ email, is_deleted: false })
        .whereNot("id", userId)
        .first();
      if (emailExists) {
        return sendResponse(res, 400, "Email already exists", false);
      }
    }

    // Username uniqueness check
    if (username) {
      const usernameExists = await db(TABLE.USERS)
        .where({ username, is_deleted: false })
        .whereNot("id", userId)
        .first();
      if (usernameExists) {
        return sendResponse(res, 400, "Username already exists", false);
      }
    }

    const updateData: any = {
      first_name,
      last_name,
      email,
      username,
      updated_at: new Date(),
    };

    if (
      req.files &&
      (req.files as { [key: string]: Express.Multer.File[] })["profile_image"]
    ) {
      const file = (req.files as { [key: string]: Express.Multer.File[] })[
        "profile_image"
      ][0];
      try {
        // Delete existing profile image if present
        if (user.profile_image) {
          await deleteFromOSS(user.profile_image);
        }

        const uploadResult = await uploadToOSS(
          file.buffer,
          file.originalname,
          file.mimetype,
          "profile-images"
        );

        if (!uploadResult) {
          return sendResponse(
            res,
            500,
            "Failed to upload profile image",
            false
          );
        }

        updateData.profile_image = uploadResult.key;
      } catch (err) {
        console.error("Error uploading image:", err);
        return sendResponse(res, 500, "Failed to upload profile image", false);
      }
    }

    await db(TABLE.USERS).where({ id: userId }).update(updateData);

    const updatedUser = await db(TABLE.USERS).where({ id: userId }).first();

    delete updatedUser.password;

    if (updatedUser.profile_image) {
      updatedUser.profile_image = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${updatedUser.profile_image}`;
    }

    return sendResponse(
      res,
      200,
      "Profile updated successfully",
      true,
      updatedUser
    );
  }
);
export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  try {
    const user = await db(TABLE.USERS)
      .where({ id: req.user?.id, is_deleted: false })
      .first();

    if (!user) {
      return sendResponse(res, 400, "User account not found", false);
    }

    delete user.password;

    if (user.profile_image) {
      user.profile_image = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${user.profile_image}`;
    }

    sendResponse(res, 200, "Profile retrieved successfully", true, user);
  } catch (error) {
    console.error("Error getting profile:", error);
    return sendResponse(res, 500, "Failed to get profile.", false);
  }
});
