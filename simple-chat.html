<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Simple Chat Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
      }
      
      .connected {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      
      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      
      .connecting {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      
      .chat-container {
        border: 1px solid #ddd;
        height: 400px;
        overflow-y: auto;
        padding: 10px;
        margin: 20px 0;
        background-color: #f9f9f9;
      }
      
      .message {
        margin: 10px 0;
        padding: 8px;
        border-radius: 5px;
      }
      
      .sent {
        background-color: #007bff;
        color: white;
        text-align: right;
        margin-left: 20%;
      }
      
      .received {
        background-color: #e9ecef;
        color: black;
        margin-right: 20%;
      }
      
      .input-group {
        display: flex;
        gap: 10px;
        margin: 10px 0;
      }
      
      input, button {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      
      button {
        background-color: #007bff;
        color: white;
        cursor: pointer;
      }
      
      button:hover {
        background-color: #0056b3;
      }
      
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
      
      .logs {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 10px;
        max-height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <h1>Simple Chat Test</h1>
    
    <div id="status" class="status connecting">Initializing...</div>
    
    <div class="input-group">
      <input type="number" id="userId" placeholder="Your User ID" value="1" />
      <input type="number" id="patientId" placeholder="Patient ID" value="1" />
      <button id="connectBtn" onclick="initializeConnection()">Connect</button>
    </div>
    
    <div class="input-group">
      <input type="number" id="receiverId" placeholder="Receiver ID" value="2" />
      <input type="text" id="messageInput" placeholder="Type your message..." />
      <button id="sendBtn" onclick="sendMessage()" disabled>Send</button>
    </div>
    
    <div id="chatContainer" class="chat-container"></div>
    
    <h3>Connection Logs:</h3>
    <div id="logs" class="logs"></div>
    
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
      let socket = null;
      let isConnected = false;
      let currentUserId = null;
      let currentPatientId = null;
      
      const statusDiv = document.getElementById('status');
      const chatContainer = document.getElementById('chatContainer');
      const logsDiv = document.getElementById('logs');
      const connectBtn = document.getElementById('connectBtn');
      const sendBtn = document.getElementById('sendBtn');
      const messageInput = document.getElementById('messageInput');
      
      function log(message) {
        const timestamp = new Date().toLocaleTimeString();
        logsDiv.innerHTML += `[${timestamp}] ${message}\n`;
        logsDiv.scrollTop = logsDiv.scrollHeight;
        console.log(message);
      }
      
      function updateStatus(message, className) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${className}`;
      }
      
      function initializeConnection() {
        const userId = document.getElementById('userId').value;
        const patientId = document.getElementById('patientId').value;
        
        if (!userId || !patientId) {
          alert('Please enter User ID and Patient ID');
          return;
        }
        
        if (socket) {
          log('Disconnecting existing socket...');
          socket.disconnect();
          socket = null;
        }
        
        currentUserId = userId;
        currentPatientId = patientId;
        
        log('Creating new socket connection...');
        updateStatus('Connecting...', 'connecting');
        
        socket = io('http://localhost:5000', {
          transports: ['websocket', 'polling'],
          timeout: 20000,
          reconnection: false, // Disable auto-reconnection to prevent loops
          forceNew: true
        });
        
        socket.on('connect', () => {
          log(`Connected with socket ID: ${socket.id}`);
          updateStatus('Connected', 'connected');
          isConnected = true;
          connectBtn.textContent = 'Disconnect';
          sendBtn.disabled = false;
          
          // Join user room
          log(`Joining room for user ${userId}...`);
          socket.emit('join', userId);
          
          // Test message send karo
          setTimeout(() => {
            log('Connection stable. Ready to send messages.');
          }, 1000);
        });
        
        socket.on('disconnect', (reason) => {
          log(`Disconnected: ${reason}`);
          updateStatus('Disconnected', 'disconnected');
          isConnected = false;
          connectBtn.textContent = 'Connect';
          sendBtn.disabled = true;
        });
        
        socket.on('connect_error', (error) => {
          log(`Connection error: ${error.message}`);
          updateStatus('Connection Error', 'disconnected');
          isConnected = false;
          connectBtn.textContent = 'Connect';
          sendBtn.disabled = true;
        });
        
        // Chat event listeners
        socket.on('newMessage', (message) => {
          log(`Received message: ${JSON.stringify(message)}`);
          displayMessage(message, false);
        });
        
        socket.on('responseError', (error) => {
          log(`Server error: ${error}`);
          alert(`Error: ${error}`);
        });
      }
      
      function sendMessage() {
        if (!isConnected || !socket) {
          alert('Not connected to server');
          return;
        }
        
        const receiverId = document.getElementById('receiverId').value;
        const message = messageInput.value.trim();
        
        if (!receiverId || !message) {
          alert('Please enter receiver ID and message');
          return;
        }
        
        const messageData = {
          sender_id: parseInt(currentUserId),
          receiver_id: parseInt(receiverId),
          patient_id: parseInt(currentPatientId),
          message: message
        };
        
        log(`Sending message: ${JSON.stringify(messageData)}`);
        socket.emit('sendMessage', messageData);
        
        // Display sent message immediately
        displayMessage({
          ...messageData,
          created_at: new Date().toISOString()
        }, true);
        
        messageInput.value = '';
      }
      
      function displayMessage(message, isSent) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
        
        const time = new Date(message.created_at).toLocaleTimeString();
        messageDiv.innerHTML = `
          <div><strong>${isSent ? 'You' : `User ${message.sender_id}`}:</strong> ${message.message}</div>
          <small>${time}</small>
        `;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
      
      // Handle Enter key in message input
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          sendMessage();
        }
      });
      
      // Update connect button functionality
      connectBtn.onclick = function() {
        if (isConnected) {
          log('Manually disconnecting...');
          socket.disconnect();
        } else {
          initializeConnection();
        }
      };
      
      // Prevent multiple instances
      window.addEventListener('beforeunload', () => {
        if (socket) {
          socket.disconnect();
        }
      });
      
      log('Simple chat initialized. Click Connect to start.');
    </script>
  </body>
</html>
