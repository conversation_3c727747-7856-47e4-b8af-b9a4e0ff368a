import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable("patients", (table) => {
    table
      .text("general_record")
      .nullable()
      .comment("Stores up to 10 keys in a JSON string format");
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable("patients", (table) => {
    table.dropColumn("general_record");
  });
}
