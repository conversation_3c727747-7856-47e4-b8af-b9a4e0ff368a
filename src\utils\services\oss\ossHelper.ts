import OSS from "../../../config/oss-config";
import { v4 as uuidv4 } from "uuid";

export const uploadToOSS = async (
  buffer: Buffer,
  originalName: string,
  mimeType: string,
  folder: string = "uploads"
) => {
  const ext = originalName.split(".").pop() || "bin";
  const fileName = `${folder}/${Date.now()}-${uuidv4()}.${ext}`;
  const result = await OSS.put(fileName, buffer, {
    headers: { "Content-Type": mimeType, "x-oss-object-acl": "public-read" },
  });
  return { key: fileName, url: result.url };
};

export const deleteFromOSS = async (key: string) => {
  const decodedKey = decodeURIComponent(key);
  return OSS.delete(decodedKey);
};

export const getSignedUrl = async (key: string, expires = 3600) => {
  const decodedKey = decodeURIComponent(key);
  return OSS.signatureUrl(decodedKey, { expires, method: "GET" });
};
