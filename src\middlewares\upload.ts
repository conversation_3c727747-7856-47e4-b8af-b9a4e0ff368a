import multer from "multer";

const allowedMimeTypes = [
  // Images
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/bmp",
  "image/tiff",

  // Documents
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "text/plain",
  "text/csv",
"application/octet-stream",
  "application/zip", // .zip
  "application/x-zip-compressed", // Windows ZIP
  "multipart/x-zip", // Older browsers
  "application/x-7z-compressed", // .7z
  "application/x-rar-compressed", // .rar
  "application/vnd.rar", // .rar (newer MIME type)
  "application/x-tar", // .tar
  "application/gzip", // .gz
  "application/x-bzip2", // .bz2
  "application/x-xz", // .xz
  "application/x-iso9660-image", // .iso
];

const fileFilter: multer.Options["fileFilter"] = (req, file, cb) => {
  console.log(
    "📥 Received file:",
    file.fieldname,
    "| MIME type:",
    file.mimetype
  );
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    console.log("❌ Rejected file:", file.fieldname);
    cb(
      new multer.MulterError(
        "LIMIT_UNEXPECTED_FILE",
        `File type ${file.mimetype} is not allowed`
      )
    );
  }
};

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024,
    files: 20,
  },
  fileFilter,
});

export default upload;
