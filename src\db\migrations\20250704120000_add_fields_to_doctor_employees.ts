import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.DOCTOR_EMPLOYEES, (table) => {
    table.string("salutation").nullable();
    table.string("practice_phone_number").nullable();
    table.string("mobile").nullable();
    table.string("profession").nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.DOCTOR_EMPLOYEES, (table) => {
    table.dropColumn("salutation");
    table.dropColumn("practice_phone_number");
    table.dropColumn("mobile");
    table.dropColumn("profession");
  });
} 