# Enhanced getAllDoctors Function - Filtering Implementation

## Overview
The `getAllDoctors` function has been enhanced to support comprehensive filtering for both active and deleted doctors based on the `is_deleted` field in the user table.

## Implementation Details

### 1. Filter Parameter
- **Query Parameter**: `filter` (optional)
- **Accepted Values**: 
  - `"active"` (default) - Returns doctors where `is_deleted = false` or `is_deleted IS NULL`
  - `"deleted"` - Returns doctors where `is_deleted = true`

### 2. API Endpoints

#### Get Active Doctors (Default)
```http
GET /admin/users/doctors
GET /admin/users/doctors?filter=active
```

#### Get Deleted Doctors
```http
GET /admin/users/doctors?filter=deleted
GET /admin/users/doctors/deleted
```

### 3. Response Format

The response now includes additional metadata:

```json
{
  "success": true,
  "message": "Active doctors fetched successfully",
  "data": {
    "doctors": [
      {
        "id": 1,
        "user_uuid": "1234567",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "specialist_id": 1,
        "is_active": true,
        "username": "johndo<PERSON>",
        "is_verified": true,
        "is_deleted": false,
        "created_at": "2025-01-01T00:00:00.000Z",
        "role": "doctor"
      }
    ],
    "filter": "active",
    "count": 1
  }
}
```

### 4. Database Query Logic

#### For Active Doctors (default behavior)
```sql
SELECT users.*, roles.role_name as role
FROM users
LEFT JOIN roles ON users.role_id = roles.id
WHERE users.role_id = [doctor_role_id]
AND (users.is_deleted = false OR users.is_deleted IS NULL)
ORDER BY users.created_at DESC
```

#### For Deleted Doctors
```sql
SELECT users.*, roles.role_name as role
FROM users
LEFT JOIN roles ON users.role_id = roles.id
WHERE users.role_id = [doctor_role_id]
AND users.is_deleted = true
ORDER BY users.created_at DESC
```

### 5. Key Features

1. **Backward Compatibility**: Existing API calls without filter parameter continue to work as before (showing only active doctors)

2. **Comprehensive Filtering**: Handles edge cases where `is_deleted` might be `NULL` (treats as active)

3. **Clear Response Messages**: Different messages for active vs deleted doctors

4. **Metadata**: Response includes filter type and count for better client-side handling

5. **Convenience Route**: Added `/doctors/deleted` route for easier access to deleted doctors

### 6. Usage Examples

#### Frontend Implementation
```javascript
// Get active doctors (default)
const activeResponse = await fetch('/api/admin/users/doctors');
const activeDoctors = await activeResponse.json();

// Get deleted doctors
const deletedResponse = await fetch('/api/admin/users/doctors?filter=deleted');
const deletedDoctors = await deletedResponse.json();

// Alternative way to get deleted doctors
const deletedResponse2 = await fetch('/api/admin/users/doctors/deleted');
const deletedDoctors2 = await deletedResponse2.json();
```

#### Admin Dashboard Integration
```javascript
// Toggle between active and deleted doctors
function toggleDoctorView(showDeleted = false) {
  const endpoint = showDeleted 
    ? '/api/admin/users/doctors?filter=deleted'
    : '/api/admin/users/doctors?filter=active';
    
  fetch(endpoint)
    .then(response => response.json())
    .then(data => {
      updateDoctorsList(data.data.doctors);
      updateStatusIndicator(data.data.filter, data.data.count);
    });
}
```

### 7. Security Considerations

- Only admin users should have access to these endpoints
- The existing authentication and authorization middleware continues to apply
- Deleted doctors data is sensitive and should only be accessible to authorized personnel

### 8. Testing

To test the implementation:

1. **Test Active Doctors**: 
   ```bash
   curl -X GET "http://localhost:3000/api/admin/users/doctors"
   ```

2. **Test Deleted Doctors**:
   ```bash
   curl -X GET "http://localhost:3000/api/admin/users/doctors?filter=deleted"
   ```

3. **Test Convenience Route**:
   ```bash
   curl -X GET "http://localhost:3000/api/admin/users/doctors/deleted"
   ```

### 9. Future Enhancements

Potential improvements that could be added later:
- Additional filter combinations (e.g., active + verified)
- Pagination support for large datasets
- Search functionality within filtered results
- Export functionality for filtered doctor lists
