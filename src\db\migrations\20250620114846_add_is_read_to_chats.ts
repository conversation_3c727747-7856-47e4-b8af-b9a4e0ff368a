import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table
      .boolean("is_read")
      .notNullable()
      .defaultTo(false)
      .comment("has this message been read by the receiver?");
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table.dropColumn("is_read");
  });
}
