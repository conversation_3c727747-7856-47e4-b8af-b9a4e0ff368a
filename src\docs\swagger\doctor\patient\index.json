{"paths": {"/doctor/patients": {"post": {"tags": ["Doctor Management"], "summary": "Create or update patient information in steps", "description": "### Step Requirements:\n1. **Step 1**: Create patient profile (Required fields: first_name, last_name, email, plan_id, dob, gender, ship_to_office, bill_to_office)\n2. **Step 2**: Clinical notes (Required fields vary by plan)\n3. **Step 3**: Upload files (Required: stlFile1, stlFile2)\n4. **Step 4**: Additional JSON data (Required: data field)\n\n### Special Cases:\n- For '4D Graphy Retainer' plan, step2 and step 4 is skipped\n- Radiographs (radioGraph1, radioGraph2) are not required for '4D Graphy Retainer' plan", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["step"], "properties": {"step": {"type": "string", "enum": ["step1", "step2", "step3", "step4"], "description": "Current step being saved"}, "id": {"type": "integer", "description": "Patient ID (required for steps 2-4)"}, "first_name": {"type": "string", "description": "<PERSON><PERSON>'s first name (required for step1)"}, "last_name": {"type": "string", "description": "<PERSON><PERSON>'s last name (required for step1)"}, "email": {"type": "string", "format": "email", "description": "Pat<PERSON>'s email (required for step1)"}, "plan_id": {"type": "integer", "description": "Treatment plan ID (required for step1)"}, "dob": {"type": "string", "format": "date", "description": "Date of birth (required for step1)"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "Patient's gender (required for step1)"}, "ship_to_office": {"type": "integer", "description": "Shipping address ID (required for step1)"}, "bill_to_office": {"type": "integer", "description": "Billing address ID (required for step1)"}, "clinical_conditions": {"type": "array", "items": {"type": "string", "enum": ["Crowding", "Spacing", "Class II Div 1", "Class II Div 2", "Class Ill", "Open Bite", "Anterior Crossbite", "Other", "Deep Bite", "Narrow Arch", "Flared <PERSON>", "Overjet", "Uneven Smile", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "description": "Array of clinical conditions (step2)"}, "general_notes": {"type": "string", "description": "General notes about patient (step2)"}, "stlFile1": {"type": "string", "format": "binary", "description": "STL file 1 (required for step3)"}, "stlFile2": {"type": "string", "format": "binary", "description": "STL file 2 (required for step3)"}, "cbctFile": {"type": "string", "format": "binary", "description": "CBCT file (optional for step3)"}, "profileRepose": {"type": "string", "format": "binary", "description": "Profile repose image (optional for step3)"}, "buccalRight": {"type": "string", "format": "binary", "description": "Buccal right image (optional for step3)"}, "buccalLeft": {"type": "string", "format": "binary", "description": "Buccal left image (optional for step3)"}, "frontalRepose": {"type": "string", "format": "binary", "description": "Frontal repose image (optional for step3)"}, "frontalSmiling": {"type": "string", "format": "binary", "description": "Frontal smiling image (optional for step3)"}, "labialAnterior": {"type": "string", "format": "binary", "description": "Labial anterior image (optional for step3)"}, "occlussalLower": {"type": "string", "format": "binary", "description": "Occlussal lower image (optional for step3)"}, "occlussalUpper": {"type": "string", "format": "binary", "description": "Occlussal upper image (optional for step3)"}, "radioGraph1": {"type": "string", "format": "binary", "description": "Radiograph 1 (optional for step3, not required for 4D Graphy Retainer)"}, "radioGraph2": {"type": "string", "format": "binary", "description": "Radiograph 2 (optional for step3, not required for 4D Graphy Retainer)"}, "case_prescription": {"type": "string", "description": "JSON string containing step 4 data (required for step4)"}}}}}}, "responses": {"200": {"description": "Step updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Step saved successfully"}, "data": {"type": "object"}}}}}}, "201": {"description": "New patient created (step 1)", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 201}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Step 1 saved"}, "data": {"type": "object"}}}}}}, "400": {"description": "Bad request - validation errors", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Missing required fields or files"}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can create or update patients"}}}}}}, "404": {"description": "Patient not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Patient not found"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Something went wrong"}}}}}}}}, "get": {"tags": ["Doctor Management"], "summary": "Get all patients for logged-in doctor", "description": "Retrieves a list of all patients associated with the authenticated doctor", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved patients list", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Patients fetched successfully"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "plan_id": {"type": "integer", "example": 1}, "plan_name": {"type": "string", "example": "4D Graphy Retainer"}, "dob": {"type": "string", "format": "date", "example": "1990-01-01"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "example": "male"}, "clinical_conditions": {"type": "array", "items": {"type": "string"}, "example": ["Crowding", "Spacing"]}, "general_notes": {"type": "string", "example": "Patient notes here"}, "stlFile1": {"type": "string", "example": "http://example.com/files/stl1.file"}, "stlFile2": {"type": "string", "example": "http://example.com/files/stl2.file"}, "cbctFile": {"type": "string", "example": "http://example.com/files/cbct.file"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}}, "401": {"description": "Unauthorized - Not logged in", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 401}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized! please login first."}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can access patient list"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/doctor/retainer/patients": {"post": {"tags": ["Doctor Management"], "summary": "Create or update retainer patient information in steps (fewer requirements than regular patients)", "description": "### Step Requirements:\n1. **Step 1**: Create patient profile (Required fields: first_name, last_name, email, plan_id, dob, gender, ship_to_office, bill_to_office)\n2. **Step 3**: Upload files (Required: stlFile1, stlFile2)\n4. ### Special Cases:\n- For '4D Graphy Retainer' plan, step2 and step 4 is skipped\n- Radiographs (radioGraph1, radioGraph2) are not required for '4D Graphy Retainer' plan", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["step"], "properties": {"step": {"type": "string", "enum": ["step1", "step3"], "description": "Current step being saved"}, "id": {"type": "integer", "description": "Patient ID (required for steps 2-4)"}, "first_name": {"type": "string", "description": "<PERSON><PERSON>'s first name (required for step1)"}, "last_name": {"type": "string", "description": "<PERSON><PERSON>'s last name (required for step1)"}, "email": {"type": "string", "format": "email", "description": "Pat<PERSON>'s email (required for step1)"}, "plan_id": {"type": "integer", "description": "Treatment plan ID (must be 4D Graphy Retainer plan, required for step1)"}, "dob": {"type": "string", "format": "date", "description": "Date of birth (required for step1)"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "Patient's gender (required for step1)"}, "ship_to_office": {"type": "integer", "description": "Shipping address ID (required for step1)"}, "bill_to_office": {"type": "integer", "description": "Billing address ID (required for step1)"}, "stlFile1": {"type": "string", "format": "binary", "description": "STL file 1 (required for step3)"}, "stlFile2": {"type": "string", "format": "binary", "description": "STL file 2 (required for step3)"}, "cbctFile": {"type": "string", "format": "binary", "description": "CBCT file (optional for step3)"}, "profileRepose": {"type": "string", "format": "binary", "description": "Profile repose image (optional for step3)"}, "buccalRight": {"type": "string", "format": "binary", "description": "Buccal right image (optional for step3)"}, "buccalLeft": {"type": "string", "format": "binary", "description": "Buccal left image (optional for step3)"}, "frontalRepose": {"type": "string", "format": "binary", "description": "Frontal repose image (optional for step3)"}, "frontalSmiling": {"type": "string", "format": "binary", "description": "Frontal smiling image (optional for step3)"}, "labialAnterior": {"type": "string", "format": "binary", "description": "Labial anterior image (optional for step3)"}, "occlussalLower": {"type": "string", "format": "binary", "description": "Occlussal lower image (optional for step3)"}, "occlussalUpper": {"type": "string", "format": "binary", "description": "Occlussal upper image (optional for step3)"}}}}}}, "responses": {"200": {"description": "Step updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Step saved successfully"}}}}}}, "201": {"description": "New retainer patient created (step 1)", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 201}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Retainer patient created"}}}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation error"}}}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Access denied"}}}}}}, "404": {"description": "Patient not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Patient not found"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Server error"}}}}}}}}}}, "/doctor/patients/{patientId}/cbct": {"post": {"tags": ["Doctor Management"], "summary": "Upload CBCT file for a patient", "security": [{"bearerAuth": []}], "parameters": [{"name": "patientId", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the patient"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"cbctFile": {"type": "string", "format": "binary", "description": "CBCT file to upload"}, "reason": {"type": "string", "description": "Reason for uploading CBCT file (optional)"}}, "required": ["cbctFile"]}}}}, "responses": {"201": {"description": "CBCT uploaded successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "404": {"description": "Patient not found"}, "500": {"description": "Internal server error"}}}}, "/doctor/patients/{patientId}/refinements": {"post": {"tags": ["Doctor Management"], "summary": "Upload refinements for a patient", "security": [{"bearerAuth": []}], "parameters": [{"name": "patientId", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the patient"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"refinementDetails": {"type": "string", "description": "Details about the refinement"}, "upperImpression": {"type": "string", "format": "binary", "description": "Upper impression file"}, "lowerImpression": {"type": "string", "format": "binary", "description": "Lower impression file"}, "profileRepose": {"type": "string", "format": "binary", "description": "Profile repose image (optional)"}, "buccalRight": {"type": "string", "format": "binary", "description": "Buccal right image (optional)"}, "buccalLeft": {"type": "string", "format": "binary", "description": "Buccal left image (optional)"}, "frontalRepose": {"type": "string", "format": "binary", "description": "Frontal repose image (optional)"}, "frontalSmiling": {"type": "string", "format": "binary", "description": "Frontal smiling image (optional)"}, "labialAnterior": {"type": "string", "format": "binary", "description": "Labial anterior image (optional)"}, "occlussalLower": {"type": "string", "format": "binary", "description": "Occlussal lower image (optional)"}, "occlussalUpper": {"type": "string", "format": "binary", "description": "Occlussal upper image (optional)"}, "radioGraph1": {"type": "string", "format": "binary", "description": "Radiograph 1 (optional)"}, "radioGraph2": {"type": "string", "format": "binary", "description": "Radiograph 2 (optional)"}}, "required": ["upperImpression", "lowerImpression"]}}}}, "responses": {"201": {"description": "Refinements uploaded successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "404": {"description": "Patient not found"}, "500": {"description": "Internal server error"}}}}, "/doctor/patients/aligner-replacements": {"post": {"tags": ["Doctor Management"], "summary": "Create aligner replacement record", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"patient_id": {"type": "integer", "description": "ID of the patient"}, "replacement_data": {"type": "string", "description": "Details about the aligner replacement"}}, "required": ["patient_id", "replacement_data"]}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> replacement added"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal server error"}}}}, "/doctor/patients/4dgraphic_retainer": {"post": {"tags": ["Doctor Management"], "summary": "Request 4D graphic retainer", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"patient_id": {"type": "integer", "description": "ID of the patient"}, "other_details": {"type": "string", "description": "Other details for the retainer request"}, "mode": {"type": "string", "enum": ["upload", "use_last"], "description": "Mode of request: upload new files or use last uploaded files"}, "stlFile1": {"type": "string", "format": "binary", "description": "STL file 1 (required if mode is upload)"}, "stlFile2": {"type": "string", "format": "binary", "description": "STL file 2 (required if mode is upload)"}, "cbctFile": {"type": "string", "format": "binary", "description": "CBCT file (optional)"}, "profileRepose": {"type": "string", "format": "binary", "description": "Profile repose image (optional)"}, "buccalRight": {"type": "string", "format": "binary", "description": "Buccal right image (optional)"}, "buccalLeft": {"type": "string", "format": "binary", "description": "Buccal left image (optional)"}, "frontalRepose": {"type": "string", "format": "binary", "description": "Frontal repose image (optional)"}, "frontalSmiling": {"type": "string", "format": "binary", "description": "Frontal smiling image (optional)"}, "labialAnterior": {"type": "string", "format": "binary", "description": "Labial anterior image (optional)"}, "occlussalLower": {"type": "string", "format": "binary", "description": "Occlussal lower image (optional)"}, "occlussalUpper": {"type": "string", "format": "binary", "description": "Occlussal upper image (optional)"}, "radioGraph1": {"type": "string", "format": "binary", "description": "Radiograph 1 (optional)"}, "radioGraph2": {"type": "string", "format": "binary", "description": "Radiograph 2 (optional)"}}, "required": ["patient_id", "mode"]}}}}, "responses": {"200": {"description": "Last scans fetched (if mode is use_last)"}, "201": {"description": "New scans saved (if mode is upload)"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "404": {"description": "No previous retainer scans found (if mode is use_last)"}, "500": {"description": "Internal server error"}}}}, "/doctor/patients/{id}": {"get": {"tags": ["Doctor Management"], "summary": "Get patient by ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the patient to retrieve"}], "responses": {"200": {"description": "Patient fetched successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "404": {"description": "Patient not found"}, "500": {"description": "Internal server error"}}}}, "/doctor/patients/shared-review/{versionId}": {"post": {"tags": ["Doctor Management"], "summary": "Review a shared patient version", "security": [{"bearerAuth": []}], "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the patient version to review"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["approve", "reject"], "description": "Action to perform: approve or reject"}, "reason": {"type": "string", "description": "Reason for approval or rejection (required if action is reject)"}}, "required": ["action"]}}}}, "responses": {"200": {"description": "Version reviewed successfully"}, "400": {"description": "Bad request"}, "403": {"description": "Forbidden"}, "404": {"description": "Version not found"}, "500": {"description": "Internal server error"}}}}}