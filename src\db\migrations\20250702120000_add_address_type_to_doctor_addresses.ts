import { Knex } from 'knex';
import { TABLE } from '../../utils/Database/table';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.DOCTOR_ADDRESSES, (table) => {
    table.string('address_type').notNullable().defaultTo('ship_to');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.DOCTOR_ADDRESSES, (table) => {
    table.dropColumn('address_type');
  });
} 