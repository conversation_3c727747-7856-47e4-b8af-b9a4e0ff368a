import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import fs from "fs";
import asyncHand<PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import validate from "../../validations";
import { loginSchema, registerSchema } from "../../validations/auth.validation";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import { changePasswordSchema } from "../../validations/auth.validation";
import { getSignedJwt } from "../../utils/services/jwt";
import { sendRegistrationEmail } from "../../utils/services/nodemailer/register";
import { sendForgotPasswordEmail } from "../../utils/services/nodemailer/forgetPassword";
import { TABLE } from "../../utils/Database/table";
import { sendVerificationEmail } from "../../utils/services/nodemailer/emailVerify";
import { resendVerificationEmail } from "../../utils/services/nodemailer/resendOtp";
import { UserRole } from "../../utils/enums/users.enum";
import { createUserSchema } from "../../validations/user.validation";
import {
  sendDoctorCredentialEmail,
  sendPasswordResetEmail,
} from "../../utils/services/nodemailer/doctorCredential";
import { sendEmailChangeVerification } from "../../utils/services/nodemailer/emailChange";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import { sendEmail } from "../../utils/services/nodemailer";
import appLogger, { errorLogger } from "../../config/logger";
import { deleteFromOSS } from "../../utils/services/oss/ossHelper";
// import { appLogger, errorLogger } from "../../utils/logging";
// this is something for testing purposes
export const createUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (
        req.user?.role !== UserRole.ADMIN &&
        req.user?.role !== UserRole.SUPERADMIN
      ) {
        sendResponse(res, 403, "Only admins can create new users", false);
        return;
      }

      const body = { ...req.body };

      const doctorRole = await db(TABLE.ROLES)
        .where("role_name", UserRole.DOCTOR)
        .first();
      const specialistRole = await db(TABLE.ROLES)
        .where("role_name", UserRole.SPECIALIST)
        .first();

      const isDoctorOrSpecialist =
        body.role_id == doctorRole?.id ||
        body.role === UserRole.DOCTOR ||
        body.role_id == specialistRole?.id ||
        body.role === UserRole.SPECIALIST;

      if (isDoctorOrSpecialist) {
        delete body.password;
      }

      const validationResult = validate(createUserSchema, body, res);
      if (!validationResult.success) {
        sendResponse(res, 400, "Validation error", false);
        return;
      }

      const { first_name, last_name, email, role_id, username, password } =
        validationResult.data;

      const userRoleId = role_id || doctorRole?.id;

      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      let finalUsername = username;
      if (!finalUsername) {
        // Generate base username from first_name and last_name
        const baseUsername = `${first_name?.toLowerCase() || ""}${
          last_name ? "." + last_name.toLowerCase() : ""
        }`.replace(/\s+/g, "");
        let tempUsername = baseUsername;
        let suffix = 1;
        // Check uniqueness
        while (
          await db(TABLE.USERS).where({ username: tempUsername }).first()
        ) {
          tempUsername = `${baseUsername}${suffix}`;
          suffix++;
        }
        finalUsername = tempUsername;
      }

      if (finalUsername) {
        const existingUsername = await db(TABLE.USERS)
          .where({ username: finalUsername })
          .first();
        if (existingUsername) {
          sendResponse(res, 400, "Username already exists", false);
          return;
        }
      }

      const roleRecord = await db(TABLE.ROLES).where("id", userRoleId).first();
      if (!roleRecord) {
        sendResponse(res, 400, "Invalid role specified", false);
        return;
      }

      if (
        ![UserRole.DOCTOR, UserRole.SPECIALIST].includes(
          roleRecord.role_name
        ) &&
        !password
      ) {
        sendResponse(
          res,
          400,
          "Password is required for non-doctor/specialist roles",
          false
        );
        return;
      }

      const generateRandomPassword = () => {
        const chars =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let pass = "";
        for (let i = 0; i < 8; i++) {
          pass += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return pass;
      };

      const finalPassword = [UserRole.DOCTOR, UserRole.SPECIALIST].includes(
        roleRecord.role_name
      )
        ? generateRandomPassword()
        : password;

      const hashedPassword = await bcrypt.hash(finalPassword as string, 10);

      // Generate 7-digit unique user_uuid
      let user_uuid: string;
      do {
        user_uuid = Math.floor(
          1_000_000 + Math.random() * 9_000_000
        ).toString();
      } while (await db(TABLE.USERS).where({ user_uuid }).first());

      console.log("🟠 Generated user_uuid:", user_uuid);

      const insertData = {
        user_uuid,
        first_name,
        last_name,
        email,
        username: finalUsername,
        is_verified: true,
        password: hashedPassword,
        role_id: roleRecord.id,
      };

      // 🟡 TRANSACTION STARTS HERE
      await db.transaction(async (trx) => {
        const [newUser] = await trx(TABLE.USERS)
          .insert(insertData)
          .returning([
            "id",
            "user_uuid",
            "first_name",
            "last_name",
            "email",
            "username",
            "role_id",
            "is_active",
            "is_verified",
          ]);

        const userData = {
          ...newUser,
          role: roleRecord.role_name,
        };

        const rolesRequiringEmail = [UserRole.DOCTOR, UserRole.SPECIALIST];

        if (rolesRequiringEmail.includes(roleRecord.role_name)) {
          try {
            await sendDoctorCredentialEmail({
              email,
              password: finalPassword as string,
              name: `${first_name} ${last_name}`,
              role: roleRecord.role_name, // Pass the role to customize the email
            });
          } catch (emailError) {
            console.error("Failed to send email:", emailError);
            // ❌ Throw to rollback
            throw new Error(
              "Email delivery failed. Please try creating the user again."
            );
          }
        }

        // ✅ If all successful
        sendResponse(
          res,
          201,
          `${roleRecord.role_name} created successfully and credentials sent via email`,
          true,
          userData
        );
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, username, password, remember } = req.body;
  const ip = (req.headers["x-forwarded-for"] as string) || req.ip || "unknown";
  const userAgent = req.get("User-Agent") || "unknown";

  appLogger.info("Login attempt initiated", {
    email: email || "not provided",
    username: username || "not provided",
    remember: !!remember,
    ip,
    userAgent,
  });

  try {
    // 1. Validate request body
    const validationResult = validate(loginSchema, req.body, res);
    if (!validationResult.success) {
      appLogger.warn("Login failed - Validation error", {
        email: email || username,
        ip,
        userAgent,
      });
      return sendResponse(res, 400, "Validation error", false);
    }

    // 2. Fetch user by email or username
    const query = db(TABLE.USERS).where({ is_deleted: false });
    if (email) query.where({ email });
    else if (username) query.where({ username });
    else {
      appLogger.warn("Login failed - No email or username provided", {
        ip,
        userAgent,
      });
      return sendResponse(res, 400, "Email or username is required", false);
    }

    const user = await query.first();
    if (!user) {
      appLogger.warn("Login failed - User not found", {
        email: email || username,
        ip,
        userAgent,
      });
      return sendResponse(res, 400, "Invalid email or password", false);
    }

    // 3. Account lock checks
    if (user.is_locked) {
      appLogger.warn("Login failed - Account locked", {
        userId: user.id,
        email: user.email,
        failedAttempts: user.failed_login_attempts,
        ip,
        userAgent,
      });
      return sendResponse(
        res,
        403,
        "Your account is temporarily locked. Please verify your email to regain access.",
        false
      );
    }

    // 4. Rate limit: 3 failed attempts → 15 sec cooldown
    if (user.failed_login_attempts >= 3 && user.last_failed_login_at) {
      const cooldownTime = 15 * 1000;
      const timeSinceLastAttempt =
        Date.now() - new Date(user.last_failed_login_at).getTime();

      if (timeSinceLastAttempt < cooldownTime) {
        const remainingTime = Math.ceil(
          (cooldownTime - timeSinceLastAttempt) / 1000
        );
        return sendResponse(
          res,
          429,
          `Too many failed attempts. Please wait ${remainingTime} seconds before trying again.`,
          false
        );
      }
    }

    // 5. Email verification & active status check
    if (!user.is_verified) {
      await sendVerificationEmail(user, res);
      return;
    }

    if (!user.is_active) {
      appLogger.warn("Login failed - Account inactive", {
        userId: user.id,
        email: user.email,
        ip,
        userAgent,
      });
      return sendResponse(res, 403, "Your account is inactive.", false);
    }

    // 6. Password check
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      // Increment failed attempts
      const newAttempts = (user.failed_login_attempts || 0) + 1;
      const updateData: any = {
        failed_login_attempts: newAttempts,
        last_failed_login_at: new Date(),
      };

      appLogger.warn("Login failed - Invalid password", {
        userId: user.id,
        email: user.email,
        failedAttempts: newAttempts,
        ip,
        userAgent,
      });

      // Lock account at 6 failed attempts
      if (newAttempts >= 6) {
        updateData.is_locked = true;
        await db(TABLE.USERS).where({ id: user.id }).update(updateData);

        appLogger.error("Account locked due to multiple failed attempts", {
          userId: user.id,
          email: user.email,
          failedAttempts: newAttempts,
          ip,
          userAgent,
        });

        return sendResponse(
          res,
          403,
          "Your account is temporarily locked. Please verify your email to regain access.",
          false
        );
      }

      await db(TABLE.USERS).where({ id: user.id }).update(updateData);

      const remainingAttempts = 6 - newAttempts;
      return sendResponse(
        res,
        400,
        `Invalid email/username or password. ${remainingAttempts} attempts remaining.` +
          (newAttempts >= 3
            ? " Please wait 15 seconds before next attempt."
            : ""),
        false
      );
    }

    // 7. Role check (EMPLOYEE must be active)

    if (user.role_id) {
      const role = await db(TABLE.ROLES).where("id", user.role_id).first();
      if (role && role.role_name === UserRole.ADMIN) {
        return sendResponse(
          res,
          403,
          "Access restricted: Admin users are not allowed here.",
          false
        );
      }
      if (role && role.role_name === UserRole.SUPERADMIN) {
        return sendResponse(
          res,
          403,
          "Access restricted: Superadmin users are not allowed here.",
          false
        );
      }
      if (role) {
        user.role = role.role_name;

        if (role.role_name === UserRole.EMPLOYEE) {
          const empRel = await db(TABLE.USER_EMPLOYEES)
            .where({ employee_id: user.id })
            .first();
          if (!empRel || empRel.status !== "active") {
            return sendResponse(
              res,
              403,
              "Please request your doctor to activate your account",
              false
            );
          }
        }
      }
    }

    // 8. Reset failed attempts if previously failed
    if (user.failed_login_attempts > 0 || user.is_locked) {
      await db(TABLE.USERS).where({ id: user.id }).update({
        failed_login_attempts: 0,
        last_failed_login_at: null,
        is_locked: false,
      });

      appLogger.info("Account unlocked after successful login", {
        userId: user.id,
        email: user.email,
        previousFailedAttempts: user.failed_login_attempts,
      });
    }

    // 9. Generate token & send success
    const accessToken = getSignedJwt(user.id, remember);

    delete user.password;
    if (user.profile_image)
      user.profile_image = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${user.profile_image}`;

    appLogger.info("Login successful", {
      userId: user.id,
      email: user.email,
      role: user.role,
      remember: !!remember,
      ip,
      userAgent,
      loginTime: new Date().toISOString(),
    });

    return sendResponse(res, 200, "Login successful", true, {
      accessToken,
      user,
    });
  } catch (err: any) {
    errorLogger.error("Login error - Unexpected exception", {
      error: err.message,
      stack: err.stack,
      email: email || username,
      ip,
      userAgent,
    });
    return sendResponse(res, 500, "Internal server error", false, err);
  }
});

export const adminLogin = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  try {
    let user;

    // Traditional email/password login
    const validationResult = validate(loginSchema, req.body, res);
    if (!validationResult.success) {
      sendResponse(res, 400, "Validation error", false);
      return;
    }

    user = await db(`${TABLE.USERS} as u`)
      .leftJoin(`${TABLE.ROLES} as r`, "u.role_id", "r.id")
      .select(
        "u.id as id",
        "u.first_name",
        "u.last_name",
        "u.email",
        "u.password",
        "u.profile_image",
        "u.is_active",
        "u.is_verified",
        "u.is_deleted",
        "u.created_at",
        "u.updated_at",
        "u.username",
        "u.role_id",
        "r.role_name"
      )
      .where({ "u.email": email, "u.is_deleted": false })
      .first();

    if (!user) {
      sendResponse(res, 400, "Invalid email or password", false);
      return;
    }

    if (
      user.role_name != UserRole.SUPERADMIN &&
      user.role_name != UserRole.ADMIN
    ) {
      sendResponse(
        res,
        403,
        "You are not authorized to perform this action",
        false
      );
      return;
    }

    if (!user.is_active) {
      sendResponse(res, 403, "Your account is inactive.", false);
      return;
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      sendResponse(res, 400, "Invalid email or password", false);
      return;
    }

    const accessToken = getSignedJwt(user.id);

    // Remove password from response
    delete user.password;

    if (user.profile_image) {
      user.profile_image = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${user.profile_image}`;
    }

    sendResponse(res, 200, "Login successful", true, {
      accessToken,
      user,
    });
  } catch (err) {
    console.error(err);
    sendResponse(res, 500, "Internal server error", false, err);
  }
});
export const verifyToken = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email, token } = req.body;

    // Validate input
    if (!email || !token) {
      sendResponse(res, 400, "Email and OTP are required.", false);
      return;
    }

    // Fetch the user
    const user = await db(TABLE.USERS)
      .where({ email, is_deleted: false })
      .first();
    if (!user) {
      sendResponse(res, 404, "Account not found.", false);
      return;
    }

    // Check if user is inactive
    if (!user.is_active) {
      sendResponse(res, 403, "Account is not inactive.", false);
      return;
    }

    // Fetch the reset token from the database
    const resetToken = await db(TABLE.PASSWORD_RESETS)
      .where({ email, token })
      .first();

    if (!resetToken) {
      sendResponse(res, 400, "Invalid or expired OTP.", false);
      return;
    }

    // Check if the OTP is expired (created more than 10 minutes ago)
    const tokenAge = Date.now() - new Date(resetToken.created_at).getTime();

    if (tokenAge > 10 * 60 * 1000) {
      sendResponse(res, 400, "OTP has been expired.", false);
      return;
    }

    await db(TABLE.PASSWORD_RESETS).where({ email }).delete();

    // If account was locked due to failed login attempts, unlock it
    if (user.is_locked) {
      await db(TABLE.USERS).where({ id: user.id }).update({
        is_locked: false,
        failed_login_attempts: 0,
        last_failed_login_at: null,
      });
      sendResponse(
        res,
        200,
        "OTP has been verified and account unlocked successfully.",
        true
      );
    } else {
      sendResponse(res, 200, "OTP has been verified.", true);
    }
  } catch (error: any) {
    console.error("OTP verification error:", error);
    sendResponse(res, 500, error.message, false);
  }
});
export const forgetPassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email, username } = req.body;

      // Validate input
      if (!email && !username) {
        sendResponse(
          res,
          400,
          "Please provide either email or username",
          false
        );
        return;
      }
       const existUser = await db(`${TABLE.USERS} as u`)
      .leftJoin(`${TABLE.ROLES} as r`, "u.role_id", "r.id")
      .select(
        "u.id as id",
        "u.email",
        "u.is_active",
        "u.is_verified",
        "u.is_deleted",
        "u.role_id",
        "r.role_name"
      )
      .where("u.is_deleted", false) 
      .andWhere(function () {
        if (email) this.orWhere("u.email", email);
        if (username) this.orWhere("u.username", username);
      }).first();

      if (
      existUser.role_name == UserRole.SUPERADMIN ||
      existUser.role_name == UserRole.ADMIN
      ) {
        sendResponse(
          res,
          403,
          "Superadmin and Admin accounts cannot reset passwords this way. Please contact support.",
          false
        );
        return;
      }

      if (!existUser) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      // Check verification status
      if (!existUser.is_verified) {
        sendResponse(res, 403, "Account is not verified", false);
        return;
      }

      // Generate random password (same as createUser function)
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      // Create and hash new password
      const newPassword = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password in database
      await db(TABLE.USERS)
        .where("id", existUser.id)
        .update({ password: hashedPassword });

      // Send email with new password
      try {
        await sendPasswordResetEmail({
          email: existUser.email,
          name: `${existUser.first_name} ${existUser.last_name}`,
          password: newPassword,
        });

        sendResponse(
          res,
          200,
          "New password sent to your registered email",
          true
        );
      } catch (emailError) {
        console.error("Email sending failed:", emailError);
        sendResponse(
          res,
          200,
          "Password reset but failed to send email. Please contact support.",
          true
        );
      }
    } catch (err) {
      console.error(err);
      sendResponse(res, 500, "Internal server error", false, err);
    }
  }
);
export const changePassword = asyncHandler(
  async (req: Request, res: Response) => {
    const validationResult = validate(changePasswordSchema, req.body, res);

    if (!validationResult.success) {
      sendResponse(res, 400, "Validation error", false);
      return;
    }

    // Check if user is logged in
    const userId = req.user?.id;

    const { password, currentPassword } = req.body;

    if (currentPassword === password) {
      sendResponse(
        res,
        400,
        "New password must be different from current password",
        false
      );
      return;
    }

    const user = await db(TABLE.USERS)
      .where({ id: userId, is_deleted: false })
      .first();

    if (!user) {
      sendResponse(res, 400, "Account not found", false);
      return;
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);

    if (!isMatch) {
      sendResponse(res, 400, "Current password is incorrect", false);
      return;
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    await db("users")
      .where({ id: userId })
      .update({ password: hashedPassword });

    sendResponse(res, 200, "Password changed successfully", true);
    return;
  }
);
export const deleteAccount = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (!userId) {
      sendResponse(res, 401, "Unauthorized", false);
      return;
    }

    try {
      const user = await db(TABLE.USERS)
        .where({ id: userId, is_deleted: false })
        .first();

      if (!user) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      if (user.profile_image) {
        await deleteFromOSS(user.profile_image);
      }

      // Use soft delete instead of hard delete
      await db(TABLE.USERS)
        .where({ id: userId })
        .update({ 
          is_deleted: true,
          updated_at: new Date()
        });

      sendResponse(res, 200, "Account deleted successfully", true);
    } catch (err) {
      console.error(err);
      sendResponse(res, 500, "Internal server error", false, err);
    }
  }
);
export const checkSession = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Check if user is authenticated via middleware
      if (!req.user) {
        sendResponse(res, 401, "Session expired or invalid", false);
        return;
      }

      // Fetch latest user status from database
      const user = await db(TABLE.USERS)
        .where({
          id: req.user.id,
          is_deleted: false,
          is_active: true,
        })
        .first();

      if (!user) {
        sendResponse(res, 401, "User not found or account deactivated", false);
        return;
      }

      // Prepare safe user data for response
      const userData = {
        id: user.id,
        email: user.email,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        role_id: user.role_id,
        is_verified: user.is_verified,
        profile_image: user.profile_image
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${user.profile_image}`
          : null,
      };

      sendResponse(res, 200, "Active session found", true, userData);
    } catch (error: any) {
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const requestEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { newEmail } = req.body;

    if (!newEmail) {
      return sendResponse(res, 400, "New email is required", false);
    }

    const exists = await db(TABLE.USERS).where({ email: newEmail }).first();
    if (exists) {
      return sendResponse(res, 400, "Email already in use", false);
    }

    const user = await db(TABLE.USERS).where({ id: userId }).first();
    if (!user) {
      return sendResponse(res, 404, "User not found", false);
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId,
        newEmail,
      },
      process.env.JWT_SECRET || "secret",
      { expiresIn: "10m" }
    );

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await db(TABLE.EMAIL_CHANGES).insert({
      user_id: userId,
      new_email: newEmail,
      token,
      expires_at: expiresAt,
      is_verified: false,
    });

    await sendEmailChangeVerification({
      currentEmail: user.email,
      newEmail,
      token,
      name: `${user.first_name} ${user.last_name}`,
    });

    sendResponse(
      res,
      200,
      "Verification token sent to your current email",
      true
    );
  }
);
export const verifyEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;

    // 1) pull token from the query
    const token = typeof req.query.token === "string" ? req.query.token : null;
    if (!token) {
      return sendResponse(res, 400, "Token is required", false);
    }

    // 2) fetch the matching unverified request
    const record = await db(TABLE.EMAIL_CHANGES)
      .where({ user_id: userId, token, is_verified: false })
      .orderBy("created_at", "desc")
      .first();

    if (!record) {
      return sendResponse(res, 400, "Invalid or expired token", false);
    }

    // 3) check expiry
    if (new Date(record.expires_at).getTime() < Date.now()) {
      return sendResponse(res, 400, "Token expired", false);
    }

    // 4) mark it as verified
    await db(TABLE.EMAIL_CHANGES).where({ id: record.id }).update({
      is_verified: true,
      verified_at: new Date(),
    });

    // 5) return the new_email from the DB record
    sendResponse(
      res,
      200,
      "Token verified. You can now confirm your new email.",
      true,
      { newEmail: record.new_email }
    );
  }
);

export const unlockAccount = asyncHandler(
  async (req: Request, res: Response) => {
    const { email, token } = req.body;

    if (!email || !token) {
      return sendResponse(
        res,
        400,
        "Email and verification token are required",
        false
      );
    }

    // Find user by email
    const user = await db(TABLE.USERS)
      .where({ email, is_deleted: false })
      .first();

    if (!user) {
      return sendResponse(res, 404, "User not found", false);
    }

    // Check if account is actually locked
    if (!user.is_locked) {
      return sendResponse(res, 400, "Account is not locked", false);
    }

    // Verify the token (you can use the same verification logic as email verification)
    // For now, we'll use a simple approach - you might want to implement proper token verification
    const verificationRecord = await db(TABLE.EMAIL_VERIFICATIONS)
      .where({ user_id: user.id, token, is_verified: false })
      .first();

    if (!verificationRecord) {
      return sendResponse(
        res,
        400,
        "Invalid or expired verification token",
        false
      );
    }

    // Check if token is expired (10 minutes)
    const tokenAge =
      Date.now() - new Date(verificationRecord.created_at).getTime();
    if (tokenAge > 10 * 60 * 1000) {
      return sendResponse(res, 400, "Verification token has expired", false);
    }

    // Unlock the account and reset failed attempts
    await db(TABLE.USERS).where({ id: user.id }).update({
      is_locked: false,
      failed_login_attempts: 0,
      last_failed_login_at: null,
    });

    // Mark verification as used
    await db(TABLE.EMAIL_VERIFICATIONS)
      .where({ id: verificationRecord.id })
      .update({ is_verified: true });

    sendResponse(
      res,
      200,
      "Account unlocked successfully. You can now login.",
      true
    );
  }
);
export const confirmEmailChange = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { newEmail } = req.body;

    if (!newEmail) {
      return sendResponse(res, 400, "New email is required", false);
    }

    // Find a verified email change record for this user and newEmail
    const record = await db(TABLE.EMAIL_CHANGES)
      .where({ user_id: userId, new_email: newEmail, is_verified: true })
      .orderBy("verified_at", "desc")
      .first();

    if (!record) {
      return sendResponse(
        res,
        400,
        "No verified email change request found",
        false
      );
    }

    // Check if new email is already taken (race condition check)
    const exists = await db(TABLE.USERS).where({ email: newEmail }).first();
    if (exists) {
      return sendResponse(res, 400, "Email already in use", false);
    }

    // Update user's email
    await db(TABLE.USERS).where({ id: userId }).update({ email: newEmail });

    sendResponse(res, 200, "Email changed successfully", true);
  }
);

export const requestUnlock = asyncHandler(
  async (req: Request, res: Response) => {
    const { email } = req.body;
    if (!email) {
      return sendResponse(res, 400, "Email is required", false);
    }

    const user = await db(TABLE.USERS)
      .where({ email, is_deleted: false })
      .first();
    if (!user) {
      return sendResponse(res, 404, "User not found", false);
    }

    if (!user.is_locked) {
      return sendResponse(res, 400, "Account is not locked", false);
    }

    // Generate a unique token
    const token = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Save token in email_verifications table
    await db(TABLE.EMAIL_VERIFICATIONS).insert({
      user_id: user.id,
      email: user.email, // fix: add email field
      token,
      is_verified: false,
      created_at: new Date(),
      expires_at: expiresAt,
    });

    // Prepare unlock link
    const unlockLink = `${process.env.BASE_URL}/api/v1/auth/unlock-account?token=${token}`;

    // Send email (reuse your sendEmail function or similar)
    await sendEmail({
      to: user.email,
      subject: "Unlock Your Account",
      html: `
      <p>Click the button below to unlock your account:</p>
      <a href="${unlockLink}" style="
        display: inline-block;
        padding: 12px 24px;
        background-color: #4CAF50;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: bold;
        margin: 10px 0;
      ">Unlock Account</a>
      <p>This link will expire in 10 minutes.</p>
    `,
      senderName: "Kitchen Konnect Team",
      senderEmail:
        process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
    });

    sendResponse(res, 200, "Unlock link sent to your email.", true);
  }
);

export const unlockAccountLink = asyncHandler(
  async (req: Request, res: Response) => {
    const { token } = req.query;
    if (!token || typeof token !== "string") {
      return res.status(400).send("Invalid or missing token.");
    }

    // Find the verification record
    const verificationRecord = await db(TABLE.EMAIL_VERIFICATIONS)
      .where({ token, is_verified: false })
      .first();

    if (!verificationRecord) {
      return res.status(400).send("Invalid or expired unlock link.");
    }

    // Check if token is expired
    if (
      verificationRecord.expires_at &&
      new Date(verificationRecord.expires_at).getTime() < Date.now()
    ) {
      return res.status(400).send("Unlock link has expired.");
    }

    // Find the user
    const user = await db(TABLE.USERS)
      .where({ id: verificationRecord.user_id })
      .first();
    if (!user) {
      return res.status(404).send("User not found.");
    }

    // Unlock the account
    await db(TABLE.USERS).where({ id: user.id }).update({
      is_locked: false,
      failed_login_attempts: 0,
      last_failed_login_at: null,
    });

    // Mark verification as used
    await db(TABLE.EMAIL_VERIFICATIONS)
      .where({ id: verificationRecord.id })
      .update({ is_verified: true });

    // Redirect to login page (frontend URL)
    const loginUrl = process.env.FRONTEND_LOGIN_URL || "/login";
    return res.redirect(loginUrl);
  }
);
