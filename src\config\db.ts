import knex from "knex";
import dotenv from "dotenv";
import { dbLogger, errorLogger } from "./logger";

dotenv.config();

const db = knex({
  client: "pg",
  connection: {
    host: process.env.DB_HOST,
    port: Number(process.env.DB_PORT),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
  },
  log: {
    warn(message) {
      dbLogger.warn('Database Warning', { message });
    },
    error(message) {
      errorLogger.error('Database Error', { message });
    },
    deprecate(message) {
      dbLogger.warn('Database Deprecation', { message });
    },
    debug(message) {
      dbLogger.debug('Database Debug', { message });
    }
  }
});

// Log successful connections
db.raw('SELECT 1')
  .then(() => {
    dbLogger.info('Database connection established successfully');
  })
  .catch((error) => {
    errorLogger.error('Database connection failed', { error: error.message, stack: error.stack });
  });

export default db;
