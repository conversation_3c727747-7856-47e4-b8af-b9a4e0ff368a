{"paths": {"/specialist/patient-versions/{versionId}/review": {"post": {"summary": "Review a patient version (approve or reject)", "tags": ["Specialist"], "parameters": [{"name": "versionId", "in": "path", "required": true, "schema": {"type": "string", "example": "1"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["approve", "reject"], "example": "approve"}, "reason": {"type": "string", "example": "Everything looks good"}, "shared_link": {"type": "string", "example": "https://drive.google.com/sample-shared-link"}, "upper_steps": {"type": "string", "example": "U1 U2 U3"}, "lower_steps": {"type": "string", "example": "L1 L2 L3"}}, "required": ["action"]}}}}, "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Review completed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Version approved successfully"}}}}}}, "400": {"description": "Bad Request (e.g., missing shared_link on approve, or missing reason on reject)"}, "403": {"description": "Only specialists are allowed to review"}, "404": {"description": "Version not found"}, "500": {"description": "Internal server error"}}}}}}