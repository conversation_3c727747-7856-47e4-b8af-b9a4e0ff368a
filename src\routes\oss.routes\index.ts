import { Router } from "express";
import upload from "../../middlewares/upload";
import {
  uploadImage,
  getImage,
  deleteRecord,
} from "../../controller/oss.controller";
import { authMiddleware } from "../../middlewares/authMiddleware";

const router = Router();

router.post(
  "/upload",
  upload.fields([{ name: "file", maxCount: 1 }]),
  uploadImage
);
router.post(
  "/upload-image",
  upload.fields([{ name: "image", maxCount: 1 }]),
  uploadImage
);
router.get("/image/:key", getImage);
router.delete("/delete", authMiddleware, deleteRecord);

export default router;
