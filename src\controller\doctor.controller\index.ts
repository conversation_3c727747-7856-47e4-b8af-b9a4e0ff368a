import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import asyncHandler from "../../middlewares/trycatch";
import validate from "../../validations";
import {
  createEmployeeSchema,
  createUserSchema,
} from "../../validations/user.validation";
import { sendEmployeeCredentialEmail } from "../../utils/services/nodemailer/employeeCredential";
import { addressSchema } from "../../validations/address.validation";
import { getIO } from "../../config/socket";
import { users } from "../../config/socket";
import { getAllPlansData, getPlanByIdData } from "../../services/plan.service";
import {
  uploadToOSS,
  deleteFromOSS,
  getSignedUrl,
} from "../../utils/services/oss/ossHelper";
import { up } from "../../db/migrations/20250304064109_create_users_table";

// Create employee under a doctor with specific role_id
export const createEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Clone body for safe mutation
      const body = { ...req.body };

      const validationResult = validate(createEmployeeSchema, body, res);
      if (!validationResult.success) {
        return;
      }

      let {
        first_name,
        last_name,
        email,
        username,
        salutation,
        practice_phone_number,
        mobile,
        profession,
      } = validationResult.data;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(res, 403, "Only doctors can create employees", false);
        return;
      }

      // Check if email already exists
      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // === Always auto-generate username from first_name and last_name ===
      let baseUsername = (first_name + last_name)
        .replace(/\s+/g, "")
        .toLowerCase();
      let candidate = baseUsername;
      let counter = 1;
      // Ensure uniqueness
      while (await db(TABLE.USERS).where({ username: candidate }).first()) {
        candidate = baseUsername + counter;
        counter++;
      }
      username = candidate;

      // Always get the EMPLOYEE role ID
      const employeeRole = await db(TABLE.ROLES)
        .where("role_name", UserRole.EMPLOYEE)
        .first();

      if (!employeeRole) {
        sendResponse(res, 400, "Employee role not found", false);
        return;
      }
      const employeeRoleId = employeeRole.id;

      // Generate random password
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      const password = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(password, 10);

      // Generate 7-digit unique user_uuid
      let user_uuid: string;
      do {
        user_uuid = Math.floor(
          1_000_000 + Math.random() * 9_000_000
        ).toString();
      } while (await db(TABLE.USERS).where({ user_uuid }).first());

      console.log("🟠 Generated user_uuid for employee:", user_uuid);

      // Create employee user
      const newEmployee = await db(TABLE.USERS)
        .insert({
          user_uuid,
          first_name,
          last_name,
          email,
          username: username || null,
          password: hashedPassword,
          role_id: employeeRoleId,
          is_verified: true,
          is_active: true,
        })
        .returning([
          "id",
          "user_uuid",
          "first_name",
          "last_name",
          "email",
          "username",
          "role_id",
        ]);

      // Create doctor-employee relationship with new fields
      await db(TABLE.USER_EMPLOYEES).insert({
        supervisor_id: req.user.id,
        employee_id: newEmployee[0].id,
        supervisor_type: "doctor",
        salutation: salutation || null,
        practice_phone_number,
        mobile: mobile || null,
        profession: profession || null,
      });

      // Get role name for the response
      const role = await db(TABLE.ROLES).where("id", employeeRoleId).first();

      const responseData = {
        ...newEmployee[0],
        role: role.role_name,
        salutation: salutation || null,
        practice_phone_number,
        mobile: mobile || null,
        profession: profession || null,
      };

      // Send credentials email
      try {
        await sendEmployeeCredentialEmail({
          email,
          password,
          name: `${first_name} ${last_name}`,
          doctorName: `${req.user.first_name} ${req.user.last_name}`,
          salutation: salutation || undefined,
          practice_phone_number,
          mobile: mobile || undefined,
          profession: profession || undefined,
        });

        sendResponse(
          res,
          201,
          "Employee created successfully and credentials sent via email",
          true,
          responseData
        );
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        sendResponse(
          res,
          201,
          "Employee created successfully but failed to send email",
          true,
          responseData
        );
      }
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Get all employees under a doctor
export const getEmployees = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can view their employees",
          false
        );
      }

      // Extract pagination params from query
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Total count for pagination (excluding soft-deleted users)
      const totalEmployeesResult = await db(TABLE.USER_EMPLOYEES)
        .join(TABLE.USERS, `${TABLE.USER_EMPLOYEES}.employee_id`, "=", `${TABLE.USERS}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "doctor")
        .where(`${TABLE.USERS}.is_deleted`, false)
        .count("employee_id as count")
        .first();

      const totalEmployees = Number(totalEmployeesResult?.count || 0);

      // Get paginated employees (excluding soft-deleted users)
      const employees = await db(TABLE.USERS)
        .join(
          TABLE.USER_EMPLOYEES,
          `${TABLE.USERS}.id`,
          "=",
          `${TABLE.USER_EMPLOYEES}.employee_id`
        )
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "doctor")
        .where(`${TABLE.USERS}.is_deleted`, false)
        .orderBy(`${TABLE.USERS}.created_at`, "desc")
        .limit(limit)
        .offset(offset)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.username`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.USERS}.user_uuid`,
          `${TABLE.ROLES}.role_name as role`,
          `${TABLE.USER_EMPLOYEES}.salutation`,
          `${TABLE.USER_EMPLOYEES}.practice_phone_number`,
          `${TABLE.USER_EMPLOYEES}.mobile`,
          `${TABLE.USER_EMPLOYEES}.profession`,
          `${TABLE.USER_EMPLOYEES}.status`, // add status
        ]);

      const formattedEmployees = employees.map((emp) => ({
        ...emp,
        profile_image: emp.profile_image
          ? process.env.BASE_URL + emp.profile_image
          : null,
        status: emp.status, // ensure status is present
      }));

      sendResponse(res, 200, "Employees retrieved successfully", true, {
        data: formattedEmployees,
        currentPage: page,
        perPage: limit,
        totalItems: totalEmployees,
        totalPages: Math.ceil(totalEmployees / limit),
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const getEmployeeById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can view their employees",
          false
        );
      }

      // Verify this employee belongs to the doctor and is not soft-deleted
      const employee = await db(TABLE.USERS)
        .join(
          TABLE.USER_EMPLOYEES,
          `${TABLE.USERS}.id`,
          "=",
          `${TABLE.USER_EMPLOYEES}.employee_id`
        )

        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "doctor")
        .where(`${TABLE.USERS}.is_deleted`, false)
        .andWhere(`${TABLE.USERS}.id`, employeeId)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.username`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.USERS}.user_uuid`,
          `${TABLE.ROLES}.role_name as role`,
          `${TABLE.USER_EMPLOYEES}.salutation`,
          `${TABLE.USER_EMPLOYEES}.practice_phone_number`,
          `${TABLE.USER_EMPLOYEES}.mobile`,
          `${TABLE.USER_EMPLOYEES}.profession`,
        ])
        .first();

      if (!employee) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }

      // Format profile image URL
      if (employee.profile_image) {
        employee.profile_image = process.env.BASE_URL + employee.profile_image;
      }

      return sendResponse(
        res,
        200,
        "Employee fetched successfully",
        true,
        employee
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
// Delete an employee under a doctor
export const deleteEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(
          res,
          403,
          "Only doctors can delete their employees",
          false
        );
        return;
      }

      // At this point, we know req.user exists and is a doctor
      const doctorId = req.user.id;

      // Verify this employee belongs to the doctor
      const relationship = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: doctorId,
          employee_id: employeeId,
          supervisor_type: "doctor",
        })
        .first();

      if (!relationship) {
        sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
        return;
      }

      // Begin transaction
      await db.transaction(async (trx) => {
        // First remove the relationship
        await trx(TABLE.USER_EMPLOYEES)
          .where({
            supervisor_id: doctorId,
            employee_id: employeeId,
            supervisor_type: "doctor",
          })
          .delete();

        // Then soft delete the user instead of hard delete
        await trx(TABLE.USERS)
          .where("id", employeeId)
          .update({ 
            is_deleted: true,
            updated_at: new Date()
          });
      });

      sendResponse(res, 200, "Employee deleted successfully", true);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Update an employee under a doctor
export const updateEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // 1) parse employeeId
      const employeeId = Number(req.params.employeeId);
      const {
        first_name,
        last_name,
        email,
        role_id,
        salutation,
        practice_phone_number,
        mobile,
        profession,
      } = req.body; // added fields

      // 2) check authorization
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can update their employees",
          false
        );
      }
      const doctorId = req.user.id;

      // 3) verify relationship

      const rel = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: doctorId,
          employee_id: employeeId,
          supervisor_type: "doctor",
        })
        .first();

      if (!rel) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }

      // 4) fetch current employee data
      const current = await db(TABLE.USERS).where("id", employeeId).first();
      if (!current) {
        return sendResponse(res, 404, "Employee record not found", false);
      }

      // 5) only check email duplicate if changed
      if (email && email !== current.email) {
        const dup = await db(TABLE.USERS)
          .where({ email })
          .whereNot("id", employeeId)
          .first();
        if (dup) {
          return sendResponse(res, 400, "Email already exists", false);
        }
      }

      // 6) build update payload (username removed)
      const updateData: any = {};
      if (first_name) updateData.first_name = first_name;
      if (last_name) updateData.last_name = last_name;
      if (email) updateData.email = email;

      // 7) handle role change
      if (role_id) {
        const roleExists = await db(TABLE.ROLES).where("id", role_id).first();
        if (!roleExists) {
          return sendResponse(res, 400, "Invalid role ID", false);
        }
        if (
          [UserRole.ADMIN, UserRole.SUPERADMIN, UserRole.DOCTOR].includes(
            roleExists.role_name as any
          )
        ) {
          return sendResponse(
            res,
            403,
            "You cannot assign this role to an employee",
            false
          );
        }
        updateData.role_id = role_id;
      }

      if (
        Object.keys(updateData).length === 0 &&
        !salutation &&
        !practice_phone_number &&
        !mobile &&
        !profession
      ) {
        return sendResponse(res, 400, "No fields to update", false);
      }

      // 8) perform update on users table
      let updated: any = current;
      if (Object.keys(updateData).length > 0) {
        const updatedRows = await db(TABLE.USERS)
          .where("id", employeeId)
          .update(updateData)
          .returning([
            "id",
            "first_name",
            "last_name",
            "email",
            "username",
            "role_id",
          ]);
        updated = updatedRows[0];
      }

      // 9) update user_employees table if any of the extra fields are present
      const userEmployeeUpdate: any = {};
      if (salutation !== undefined) userEmployeeUpdate.salutation = salutation;
      if (practice_phone_number !== undefined)
        userEmployeeUpdate.practice_phone_number = practice_phone_number;
      if (mobile !== undefined) userEmployeeUpdate.mobile = mobile;
      if (profession !== undefined) userEmployeeUpdate.profession = profession;
      if (Object.keys(userEmployeeUpdate).length > 0) {
        await db(TABLE.USER_EMPLOYEES)
          .where({
            supervisor_id: doctorId,
            employee_id: employeeId,
            supervisor_type: "doctor",
          })
          .update(userEmployeeUpdate);
      }

      const role = await db(TABLE.ROLES).where("id", updated.role_id).first();

      // fetch latest user_employees data for response
      const userEmp = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: doctorId,
          employee_id: employeeId,
          supervisor_type: "doctor",
        })
        .first();

      return sendResponse(res, 200, "Employee updated successfully", true, {
        ...updated,
        role: role.role_name,
        ...userEmp,
      });
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const updateEmployeeStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const employeeId = Number(req.params.employeeId);
      const { status } = req.body;
      const allowed = ["active", "inactive"];
      if (!status || !allowed.includes(status)) {
        return sendResponse(
          res,
          400,
          `Status must be one of: ${allowed.join(", ")}`,
          false
        );
      }
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can update employee status",
          false
        );
      }
      const doctorId = req.user.id;
      // Verify relationship
      const rel = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: doctorId,
          employee_id: employeeId,
          supervisor_type: "doctor",
        })
        .first();
      if (!rel) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }
      // Update status
      await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: doctorId,
          employee_id: employeeId,
          supervisor_type: "doctor",
        })
        .update({ status });
      return sendResponse(
        res,
        200,
        "Employee status updated successfully",
        true,
        { employeeId, status }
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const addDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if doctor is authorized
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can add addresses", false);
      return;
    }

    const validationResult = validate(addressSchema, req.body, res);
    if (!validationResult.success) {
      return;
    }

    const [address] = await db("doctor_addresses")
      .insert({
        doctor_id: req.user.id,
        ...validationResult.data,
      })
      .returning("*");

    // Determine success message based on address type
    let successMessage = "Address added successfully"; // default message
    if (validationResult.data.address_type === "bill_to") {
      successMessage = "Billing address added successfully";
    } else if (validationResult.data.address_type === "ship_to") {
      successMessage = "Shipping address added successfully";
    }

    sendResponse(res, 201, successMessage, true, address);
  }
);
export const getDoctorAddresses = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if user is doctor or employee
    if (!req.user) {
      sendResponse(res, 401, "Unauthorized", false);
      return;
    }

    let doctorId: number;

    if (req.user.role === UserRole.DOCTOR) {
      // If user is a doctor, use their ID
      doctorId = req.user.id;
    } else if (req.user.role === UserRole.EMPLOYEE) {
      // If user is an employee, find their supervisor (doctor)
      const employeeRelation = await db(TABLE.USER_EMPLOYEES)
        .where({
          employee_id: req.user.id,
          supervisor_type: "doctor",
          status: "active",
        })
        .first();

      if (!employeeRelation) {
        sendResponse(
          res,
          403,
          "Employee not associated with any doctor or account is inactive",
          false
        );
        return;
      }

      doctorId = employeeRelation.supervisor_id;
    } else {
      sendResponse(
        res,
        403,
        "Only doctors and employees can view addresses",
        false
      );
      return;
    }

    const addresses = await db("doctor_addresses")
      .where("doctor_id", doctorId)
      .select("*")
      .orderBy("created_at", "desc");

    sendResponse(res, 200, "Addresses retrieved", true, addresses);
  }
);
export const updateDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can update addresses", false);
      return;
    }

    const { addressId } = req.params;

    // Verify address belongs to this doctor
    const address = await db("doctor_addresses")
      .where({
        id: addressId,
        doctor_id: req.user.id,
      })
      .first();

    if (!address) {
      sendResponse(
        res,
        404,
        "Address not found or doesn't belong to you",
        false
      );
      return;
    }

    const validationResult = validate(addressSchema, req.body, res);
    if (!validationResult.success) {
      return;
    }

    const [updatedAddress] = await db("doctor_addresses")
      .where("id", addressId)
      .update(validationResult.data)
      .returning("*");

    // Determine success message based on address type
    let successMessage = "Address updated successfully"; // default message
    if (validationResult.data.address_type === "bill_to") {
      successMessage = "Billing address updated successfully";
    } else if (validationResult.data.address_type === "ship_to") {
      successMessage = "Shipping address updated successfully";
    }

    sendResponse(
      res,
      200,
      successMessage,
      true,
      updatedAddress
    );
  }
);
export const deleteDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can delete addresses", false);
      return;
    }
    const { addressId } = req.params;
    // Verify address belongs to this doctor
    const address = await db("doctor_addresses")
      .where({
        id: addressId,
        doctor_id: req.user.id,
      })
      .first();

    if (!address) {
      sendResponse(
        res,
        404,
        "Address not found or doesn't belong to you",
        false
      );
      return;
    }

    await db("doctor_addresses").where("id", addressId).delete();

    sendResponse(res, 200, "Address deleted successfully", true);
  }
);
export const getDoctorAddressById = asyncHandler(
  async (req: Request, res: Response) => {
    const { addressId } = req.params;
    const address = await db("doctor_addresses")
      .where({ id: addressId })
      .first();
    if (!address) {
      sendResponse(res, 404, "Address not found", false);
      return;
    }
    sendResponse(res, 200, "Address fetched successfully", true, address);
  }
);
export const savePatientStep = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const data: any = req.body;
      const step = ["step1", "step2", "step3", "step4", "step5"].includes(
        data.step
      )
        ? data.step
        : undefined;
      if (!step)
        return sendResponse(res, 400, "Invalid or missing step", false);

      // Check if user is doctor or employee
      if (!req.user) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      let doctorId: number;

      if (req.user.role === UserRole.DOCTOR) {
        // If user is a doctor, use their ID
        doctorId = req.user.id;
      } else if (req.user.role === UserRole.EMPLOYEE) {
        // If user is an employee, find their supervisor (doctor)
        const employeeRelation = await db(TABLE.USER_EMPLOYEES)
          .where({
            employee_id: req.user.id,
            supervisor_type: "doctor",
            status: "active",
          })
          .first();

        if (!employeeRelation) {
          return sendResponse(
            res,
            403,
            "Employee not associated with any doctor or account is inactive",
            false
          );
        }

        doctorId = employeeRelation.supervisor_id;
      } else {
        return sendResponse(
          res,
          403,
          "Only doctors and employees can create or update patients",
          false
        );
      }

      const patientId = data.id ? Number(data.id) : undefined;

      async function sendSocketNotificationToSpecialist(
        specialistId: any,
        notification: any,
        io: any
      ) {
        const userSockets = users.find(
          (u) => u.userId === String(specialistId)
        );
        console.log("Found userSockets:", userSockets);

        if (userSockets) {
          const unreadCount = await db(TABLE.NOTIFICATIONS)
            .where({ receiver_id: specialistId, is_read: false })
            .count("id as count")
            .first()
            .then((r: any) => parseInt(r.count) || 0);

          userSockets.socketIds.forEach((socketId) => {
            console.log("Emitting to socket:", socketId);
            io.to(socketId).emit("notification", {
              notifications: [notification],
              unread_count: unreadCount,
            });
          });
        } else {
          console.log(`No socket found for specialist: ${specialistId}`);
        }
      }

      if (step === "step1") {
        const required = [
          "first_name",
          "last_name",
          "plan_id",
          "dob",
          "country",
          "gender",
          "ship_to_office",
          "bill_to_office",
        ];
        const missing = required.filter(
          (f) => data[f] == null || data[f] === ""
        );

        if (missing.length)
          return sendResponse(
            res,
            400,
            `Missing required fields: ${missing.join(", ")}`,
            false
          );

        let uuid: string;
        do {
          uuid = Math.floor(1_000_000 + Math.random() * 9_000_000).toString();
        } while (await db(TABLE.PATIENTS).where({ uuid }).first());

        const plan = await db(TABLE.PLANS).where({ id: data.plan_id }).first();
        if (!plan) return sendResponse(res, 400, "Invalid plan_id", false);

        const shipAddr = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: data.ship_to_office, doctor_id: doctorId })
          .first();
        if (!shipAddr)
          return sendResponse(res, 400, "Invalid ship_to_office", false);

        const billAddr = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: data.bill_to_office, doctor_id: doctorId })
          .first();
        if (!billAddr)
          return sendResponse(res, 400, "Invalid bill_to_office", false);

        if (data.email) {
          const existing = await db(TABLE.PATIENTS)
            .where({ email: data.email, doctor_id: doctorId })
            .first();
          if (existing) {
            return sendResponse(
              res,
              400,
              "A patient with this email already exists",
              false
            );
          }
        }

        if (patientId) {
          // update
          await db(TABLE.PATIENTS)
            .where({ id: patientId, doctor_id: doctorId })
            .update({
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email || null,
              plan_id: data.plan_id,
              dob: data.dob,
              gender: data.gender,
              ship_to_office: data.ship_to_office,
              bill_to_office: data.bill_to_office,
              country: data.country,
            });

          const patient = await db(TABLE.PATIENTS)
            .where({ id: patientId, doctor_id: doctorId })
            .first();

          return sendResponse(res, 200, "Step 1 updated", true, patient);
        } else {
          // create
          const [newPat] = await db(TABLE.PATIENTS)
            .insert({
              doctor_id: doctorId,
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email || null,
              plan_id: data.plan_id,
              dob: data.dob,
              gender: data.gender,
              ship_to_office: data.ship_to_office,
              bill_to_office: data.bill_to_office,
              country: data.country,
              uuid: uuid,
            })
            .returning("*");

          // Notify all specialists
          const specialists = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.ROLES}.role_name`, "specialist")
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`);

          const io = getIO();
          const notificationPromises = specialists.map(async (specialist) => {
            const [notification] = await db(TABLE.NOTIFICATIONS)
              .insert({
                receiver_id: specialist.id,
                user_id: req.user?.id,
                title: "New Patient Created",
                message: `New patient ${data.first_name} ${data.last_name} created by Dr. ${req.user?.first_name} ${req.user?.last_name}`,
                details: JSON.stringify({
                  type: "patient_created",
                  doctor_id: req?.user?.id,
                  patient_id: newPat?.id,
                  patient_name: `${data.first_name} ${data.last_name}`,
                  doctor_name: `${req.user?.first_name} ${req.user?.last_name}`,
                }),
              })
              .returning("*");

            await sendSocketNotificationToSpecialist(
              specialist.id,
              notification,
              io
            );

            return notification;
          });

          if (req.user) {
            await Promise.all(notificationPromises);
          }

          return sendResponse(res, 201, "Step 1 created", true, newPat);
        }
      }

      // === STEP 2 ===
      if (step === "step2") {
        if (!patientId)
          return sendResponse(res, 400, "Missing patient id", false);

        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        const plan = pat.plan_id
          ? await db(TABLE.PLANS).where({ id: pat.plan_id }).first()
          : null;

        if (plan && plan.name === "4D Graphy Retainer") {
          return sendResponse(
            res,
            200,
            "Step 2 skipped for selected plan",
            true,
            pat
          );
        }

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .update({
            clinical_conditions: data.clinical_conditions,
            general_notes: data.general_notes,
          });
        return sendResponse(res, 200, "Step 2 saved", true);
      }

      if (step === "step3") {
        if (!patientId)
          return sendResponse(res, 400, "Missing patient id", false);

        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .first();

        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        const plan = pat.plan_id
          ? await db(TABLE.PLANS).where({ id: pat.plan_id }).first()
          : null;

        if (!req.files || typeof req.files !== "object") {
          return sendResponse(res, 400, "Files are required for step3", false);
        }

        const files = req.files as unknown as Record<
          string,
          Express.Multer.File[]
        >;

        type FileField = { key: keyof typeof files; folder: string };

        const fileFields: FileField[] = [
          { key: "stlFile1", folder: "stlFiles" },
          { key: "stlFile2", folder: "stlFiles" },
          { key: "cbctFile", folder: "cbctFiles" },
          { key: "profileRepose", folder: "retainerImages" },
          { key: "buccalRight", folder: "retainerImages" },
          { key: "buccalLeft", folder: "retainerImages" },
          { key: "frontalRepose", folder: "retainerImages" },
          { key: "frontalSmiling", folder: "retainerImages" },
          { key: "labialAnterior", folder: "retainerImages" },
          { key: "occlussalLower", folder: "retainerImages" },
          { key: "occlussalUpper", folder: "retainerImages" },
          { key: "radioGraph1", folder: "radioGraphs" },
          { key: "radioGraph2", folder: "radioGraphs" },
        ];

        const updatePayload: any = {};

        for (const { key, folder } of fileFields) {
          const file = files[key]?.[0];

          if (file) {
            if (pat[key]) {
              await deleteFromOSS(pat[key]);
            }
            const uploaded = await uploadToOSS(
              file.buffer,
              file.originalname,
              file.mimetype,
              folder
            );
            updatePayload[key] = uploaded.key;
          } else if (pat[key] && !files[key]) {
            updatePayload[key] = pat[key];
          }
        }

        const uploadedGeneralRecords = files["generalRecords"] || [];
        let existingGeneral = [];

        if (pat.general_record) {
          try {
            existingGeneral = JSON.parse(pat.general_record);
            if (!Array.isArray(existingGeneral)) existingGeneral = [];
          } catch {
            existingGeneral = [];
          }
        }

        const generalRecordKeys: string[] = [];

        for (let i = 0; i < Math.min(uploadedGeneralRecords.length, 10); i++) {
          const file = uploadedGeneralRecords[i];
          const uploaded = await uploadToOSS(
            file.buffer,
            file.originalname,
            file.mimetype,
            "generalRecords"
          );
          if (uploaded?.key) {
            generalRecordKeys.push(uploaded.key);
          }
        }

        // Delete old general records if replaced
        if (generalRecordKeys.length > 0) {
          for (const oldKey of existingGeneral) {
            await deleteFromOSS(oldKey);
          }
          updatePayload.general_record = JSON.stringify(generalRecordKeys);
        } else {
          updatePayload.general_record = JSON.stringify(existingGeneral); // Keep if no update
        }

        // Validate required fields
        const requiredFields = ["stlFile1", "stlFile2"];
        const optionalFields = [
          "profileRepose",
          "buccalRight",
          "buccalLeft",
          "frontalRepose",
          "frontalSmiling",
          "labialAnterior",
          "occlussalLower",
          "occlussalUpper",
          "cbctFile",
        ];

        if (!plan || plan.name !== "4D Graphy Retainer") {
          optionalFields.push("radioGraph1", "radioGraph2");
        }

        const missingRequired = requiredFields.filter(
          (field) => !updatePayload[field] && !pat[field]
        );

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .update(updatePayload);

        const planType = await db(TABLE.PLANS)
          .where({ id: pat.plan_id })
          .first();

        if (planType.type == "retainer") {
          // Create version after step 3 completion for all plans
          const lastVer = await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patientId })
            .orderBy("version_number", "desc")
            .first();

          const nextVerNum = lastVer ? Number(lastVer.version_number) + 1 : 1;

          await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patientId })
            .update({ is_latest_version: false });

          // Get updated patient data for version
          const updatedPatientData = await db(TABLE.PATIENTS)
            .where({ id: patientId, doctor_id: doctorId })
            .first();

          const versionTitle =
            plan && plan.name === "4D Graphy Retainer"
              ? `Retainer V${nextVerNum}`
              : `Initial V${nextVerNum}`;

          await db(TABLE.PATIENTS_VERSIONS).insert({
            patient_id: patientId,
            created_by: doctorId, // Always use doctor ID for ownership
            employee_id:
              req.user.role === UserRole.EMPLOYEE ? req.user.id : null, // Track employee if applicable
            version_number: nextVerNum,
            title: versionTitle,
            status: "sent_by_doctor",
            upper_steps: null,
            lower_steps: null,
            approval_reason: null,
            rejection_reason: null,
            is_latest_version: true,
            data: JSON.stringify({ patient: updatedPatientData }),
          });
        }

        // Send notification to specialist for all plans
        let specialist;
        const userWithSpecialist = req.user as typeof req.user & {
          specialist_id?: number;
        };
        if (userWithSpecialist.specialist_id) {
          // First try to get the assigned specialist
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.USERS}.id`, userWithSpecialist.specialist_id)
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        // If no assigned specialist or not found, fall back to any active specialist
        if (!specialist) {
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.ROLES}.role_name`, "specialist")
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        if (specialist) {
          // Get doctor information for notification
          const doctorInfo = await db(TABLE.USERS)
            .where("id", doctorId)
            .select("first_name", "last_name")
            .first();

          const isRetainerPlan = plan && plan.name === "4D Graphy Retainer";
          let notificationMessage = isRetainerPlan
            ? `Retainer file submitted by Dr. ${doctorInfo.first_name}`
            : `Patient file submitted by Dr. ${doctorInfo.first_name}`;

          // If action was performed by employee, include that information
          if (req.user.role === UserRole.EMPLOYEE) {
            notificationMessage += ` (via employee: ${req.user.first_name} ${req.user.last_name})`;
          }

          const notificationTitle = isRetainerPlan
            ? "Retainer File Ready for Review"
            : "Patient File Ready for Review";

          const [notification] = await db(TABLE.NOTIFICATIONS)
            .insert({
              receiver_id: specialist.id,
              user_id: doctorId, // Always use doctor ID as the main user
              title: notificationTitle,
              message: notificationMessage,
              details: JSON.stringify({
                type: isRetainerPlan
                  ? "retainer_file_ready"
                  : "patient_file_ready",
                doctor_id: doctorId,
                employee_id:
                  req.user.role === UserRole.EMPLOYEE ? req.user.id : null,
                patient_id: patientId,
              }),
            })
            .returning("*");

          const io = getIO();
          const userSockets = users.find(
            (u) => u.userId === String(specialist.id)
          );
          if (userSockets) {
            const unreadCount = await db(TABLE.NOTIFICATIONS)
              .where({ receiver_id: specialist.id, is_read: false })
              .count("id as count")
              .first()
              .then((r: any) => parseInt(r.count) || 0);

            userSockets.socketIds.forEach((sid) =>
              io.to(sid).emit("notification", {
                notifications: [notification],
                unread_count: unreadCount,
              })
            );
          }
        }

        const updatedPat = await db(TABLE.PATIENTS)
          .where({ id: patientId })
          .first();
        return sendResponse(res, 200, "Step 3 saved", true, updatedPat);
      }

      // === STEP 4 ===
      if (step === "step4") {
        const isNew = !patientId;
        if (isNew && !data.case_prescription) {
          return sendResponse(
            res,
            400,
            "Missing case_prescription for new patient",
            false
          );
        }
        if (!isNew && !data.case_prescription) {
          return sendResponse(res, 400, "Missing case_prescription", false);
        }

        // parse JSON
        let step4Json: any;
        try {
          step4Json =
            typeof data.case_prescription === "string"
              ? JSON.parse(data.case_prescription)
              : data.case_prescription;
        } catch {
          return sendResponse(
            res,
            400,
            "Invalid JSON in case prescription",
            false
          );
        }

        // --- NEW PATIENT FLOW ---
        if (isNew) {
          const [newPat] = await db(TABLE.PATIENTS)
            .insert({
              doctor_id: doctorId,
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email || null,
              plan_id: data.plan_id,
              dob: data.dob,
              gender: data.gender,
              ship_to_office: data.ship_to_office,
              bill_to_office: data.bill_to_office,
              data: JSON.stringify(step4Json),
            })
            .returning("*");

          await db(TABLE.PATIENTS_VERSIONS).insert({
            patient_id: newPat.id,
            created_by: doctorId, // Always use doctor ID for ownership
            employee_id:
              req.user?.role === UserRole.EMPLOYEE ? req.user?.id : null, // Track employee if applicable
            version_number: 1,
            title: "Initial V1",
            status: "sent_by_doctor",
            upper_steps: data.upper_steps || null,
            lower_steps: data.lower_steps || null,
            approval_reason: null,
            rejection_reason: null,
            is_latest_version: true,
            data: JSON.stringify({ patient: newPat }),
          });

          return sendResponse(
            res,
            201,
            "Step 4 created (initial)",
            true,
            newPat
          );
        }

        // --- EXISTING PATIENT FLOW ---
        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .update({ data: JSON.stringify(step4Json) });

        const lastVer = await db(TABLE.PATIENTS_VERSIONS)
          .where({ patient_id: patientId })
          .orderBy("version_number", "desc")
          .first();

        const nextVerNum = lastVer ? Number(lastVer.version_number) + 1 : 1;

        await db(TABLE.PATIENTS_VERSIONS)
          .where({ patient_id: patientId })
          .update({ is_latest_version: false });

        await db(TABLE.PATIENTS_VERSIONS).insert({
          patient_id: patientId,
          created_by: doctorId, // Always use doctor ID for ownership
          employee_id:
            req.user?.role === UserRole.EMPLOYEE ? req.user?.id : null, // Track employee if applicable
          version_number: nextVerNum,
          title: `Initial V${nextVerNum}`,
          status: "sent_by_doctor",
          upper_steps: data.upper_steps || null,
          lower_steps: data.lower_steps || null,
          approval_reason: null,
          rejection_reason: null,
          is_latest_version: true,
          data: JSON.stringify(step4Json),
        });

        let specialist;
        const userWithSpecialist = req.user as typeof req.user & {
          specialist_id?: number;
        };
        if (userWithSpecialist.specialist_id) {
          // First try to get the assigned specialist
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.USERS}.id`, userWithSpecialist.specialist_id)
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        // If no assigned specialist or not found, fall back to any active specialist
        if (!specialist) {
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.ROLES}.role_name`, "specialist")
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        if (specialist) {
          // Get doctor information for notification
          const doctorInfo = await db(TABLE.USERS)
            .where("id", doctorId)
            .select("first_name", "last_name")
            .first();

          let notificationMessage = `Patient file submitted by Dr. ${doctorInfo.first_name}`;

          // If action was performed by employee, include that information
          if (req.user?.role === UserRole.EMPLOYEE) {
            notificationMessage += ` (via employee: ${req.user?.first_name} ${req.user?.last_name})`;
          }

          const [notification] = await db(TABLE.NOTIFICATIONS)
            .insert({
              receiver_id: specialist.id,
              user_id: doctorId, // Always use doctor ID as the main user
              title: "Patient File Ready for Review",
              message: notificationMessage,
              details: JSON.stringify({
                type: "patient_file_ready",
                doctor_id: doctorId,
                employee_id:
                  req.user?.role === UserRole.EMPLOYEE ? req.user?.id : null,
                patient_id: patientId,
              }),
            })
            .returning("*");

          const io = getIO();
          const userSockets = users.find(
            (u) => u.userId === String(specialist.id)
          );
          if (userSockets) {
            const unreadCount = await db(TABLE.NOTIFICATIONS)
              .where({ receiver_id: specialist.id, is_read: false })
              .count("id as count")
              .first()
              .then((r: any) => parseInt(r.count) || 0);

            userSockets.socketIds.forEach((sid) =>
              io.to(sid).emit("notification", {
                notifications: [notification],
                unread_count: unreadCount,
              })
            );
          }
        }

        // Additional notification for patient status update
        if (specialist) {
          const io = getIO();

          const [statusNotification] = await db(TABLE.NOTIFICATIONS)
            .insert({
              receiver_id: specialist.id,
              user_id: req.user?.id,
              title: "Patient Status Updated",
              message: `Patient ${pat.first_name} ${pat.last_name} status updated to version ${nextVerNum}`,
              details: JSON.stringify({
                type: "patient_status_updated",
                doctor_id: req.user?.id,
                patient_id: patientId,
                patient_name: `${pat.first_name} ${pat.last_name}`,
                doctor_name: `${req.user?.first_name} ${req.user?.last_name}`,
                version_number: nextVerNum,
                status: "sent_by_doctor",
              }),
            })
            .returning("*");

          const userSockets = users.find(
            (u) => u.userId === String(specialist.id)
          );
          if (userSockets) {
            const unreadCount = await db(TABLE.NOTIFICATIONS)
              .where({ receiver_id: specialist.id, is_read: false })
              .count("id as count")
              .first()
              .then((r: any) => parseInt(r.count) || 0);

            userSockets.socketIds.forEach((socketId) =>
              io.to(socketId).emit("notification", {
                notifications: [statusNotification],
                unread_count: unreadCount,
              })
            );
          }
        }

        return sendResponse(res, 200, "Step 4 saved", true);
      }

      // === STEP 5 ===
      if (step === "step5") {
        if (!patientId)
          return sendResponse(res, 400, "Missing patient id", false);

        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: doctorId })
          .update({
            sets: data.sets,
            archRequired: data.archRequired,
            notes: data.notes,
          });
        return sendResponse(res, 200, "Step 5 saved", true);
      }

      // unsupported
      return sendResponse(res, 400, "Unsupported step", false);
    } catch (error: any) {
      console.error(error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);

export const getPatientById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const patientId = Number(req.params.id);
      if (!patientId) {
        return sendResponse(res, 400, "Patient ID is required", false);
      }

      // Check if user is doctor or employee
      if (!req.user) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      let doctorId: number;

      if (req.user.role === UserRole.DOCTOR) {
        // If user is a doctor, use their ID
        doctorId = req.user.id;
      } else if (req.user.role === UserRole.EMPLOYEE) {
        // If user is an employee, find their supervisor (doctor)
        const employeeRelation = await db(TABLE.USER_EMPLOYEES)
          .where({
            employee_id: req.user.id,
            supervisor_type: "doctor",
            status: "active",
          })
          .first();

        if (!employeeRelation) {
          return sendResponse(
            res,
            403,
            "Employee not associated with any doctor or account is inactive",
            false
          );
        }

        doctorId = employeeRelation.supervisor_id;
      } else {
        return sendResponse(
          res,
          403,
          "Only doctors and employees can access patient data",
          false
        );
      }

      // 1. Patient basic info
      const patient = await db(TABLE.PATIENTS)
        .where({ id: patientId, doctor_id: doctorId })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // 1b. Doctor info (get name)
      let doctorName = null;
      if (patient.doctor_id) {
        const doctor = await db(TABLE.USERS)
          .where({ id: patient.doctor_id })
          .select("first_name", "last_name")
          .first();
        if (doctor) {
          doctorName = `${doctor.first_name} ${doctor.last_name}`;
        }
      }

      // 1c. Specialist info (get name)
      let specialistName = null;
      let specialistId = null;
      // Try to get specialist_id from latestVersion or patient
      let specialist_id = null;
      // We'll set this after fetching latestVersion below

      // 2. Plan info
      let plan = null;
      if (patient.plan_id) {
        plan = await db(TABLE.PLANS).where({ id: patient.plan_id }).first();
      }

      // 3. Latest patient version status
      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "desc")
        .first();
      const patientStatus = latestVersion ? latestVersion.status : null;

      if (latestVersion && latestVersion.specialist_id) {
        specialist_id = latestVersion.specialist_id;
      } else if (patient.specialist_id) {
        specialist_id = patient.specialist_id;
      }
      if (specialist_id) {
        specialistId = specialist_id;
        const specialist = await db(TABLE.USERS)
          .where({ id: specialist_id })
          .select("first_name", "last_name")
          .first();
        if (specialist) {
          specialistName = `${specialist.first_name} ${specialist.last_name}`;
        }
      }

      // 3b. All versions for this patient
      const allVersions = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "asc");

      // 3. Doctor addresses
      let shipToOffice = null,
        billToOffice = null;
      if (patient.ship_to_office) {
        shipToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.ship_to_office })
          .first();
      }
      if (patient.bill_to_office) {
        billToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.bill_to_office })
          .first();
      }

      // 4. Related tables
      const refinements = await db(TABLE.REFINEMENTS).where({
        patient_id: patientId,
      });
      const alignerReplacements = await db("aligner_replacements").where({
        patient_id: patientId,
      });
      const retainers = await db("four_d_graphy_retainers").where({
        patient_id: patientId,
      });
      const refinementsAligner = await db("refinements_aligner").where({
        patient_id: patientId,
      });
      const patientFiles = await db("patient_files").where({
        patient_id: patientId,
      });

      // 5. Parse clinical conditions
      let clinicalConditions: string[] | null = null;
      if (patient.clinical_conditions) {
        try {
          clinicalConditions = JSON.parse(patient.clinical_conditions);
        } catch {
          clinicalConditions = null;
        }
      }

      // 6. File fields with URLs
      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      const enrichedPatient: any = {
        ...patient,
        clinical_conditions: clinicalConditions,
        plan,
        shipToOffice,
        billToOffice,
        refinements,
        alignerReplacements,
        retainers,
        refinementsAligner,
        patientFiles,
        status: patientStatus,
        versions: allVersions, // <-- add all versions here
        doctor_id: patient.doctor_id,
        doctor_name: doctorName,
        specialist_id: specialistId,
        specialist_name: specialistName,
      };

      for (const field of fileFields) {
        if (patient[field]) {
          enrichedPatient[
            field
          ] = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${patient[field]}`;
        }
      }

      // For retainers
      enrichedPatient.retainers = await Promise.all(
        retainers.map(async (r) => ({
          ...r,
          stl_file1: r.stl_file1
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.stl_file1}`
            : null,
          stl_file2: r.stl_file2
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.stl_file2}`
            : null,
          profile_repose: r.profile_repose
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.profile_repose}`
            : null,
          buccal_right: r.buccal_right
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccal_right}`
            : null,
          buccal_left: r.buccal_left
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccal_left}`
            : null,
          frontal_repose: r.frontal_repose
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontal_repose}`
            : null,
          frontal_smiling: r.frontal_smiling
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontal_smiling}`
            : null,
          labial_anterior: r.labial_anterior
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.labial_anterior}`
            : null,
          occlusal_lower: r.occlusal_lower
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlusal_lower}`
            : null,
          occlusal_upper: r.occlusal_upper
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlusal_upper}`
            : null,
        }))
      );

      // For refinementsAligner
      enrichedPatient.refinementsAligner = await Promise.all(
        refinementsAligner.map(async (r) => ({
          ...r,
          upper_impression: r.upper_impression
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.upper_impression}`
            : null,
          lower_impression: r.lower_impression
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.lower_impression}`
            : null,
          profileRepose: r.profileRepose
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.profileRepose}`
            : null,
          buccalRight: r.buccalRight
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccalRight}`
            : null,
          buccalLeft: r.buccalLeft
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccalLeft}`
            : null,
          frontalRepose: r.frontalRepose
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontalRepose}`
            : null,
          frontalSmiling: r.frontalSmiling
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontalSmiling}`
            : null,
          labialAnterior: r.labialAnterior
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.labialAnterior}`
            : null,
          occlussalLower: r.occlussalLower
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlussalLower}`
            : null,
          occlussalUpper: r.occlussalUpper
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlussalUpper}`
            : null,
          radioGraph1: r.radioGraph1
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.radioGraph1}`
            : null,
          radioGraph2: r.radioGraph2
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.radioGraph2}`
            : null,
        }))
      );
      try {
        const generalRecordArray: string[] = JSON.parse(
          patient.general_record || "[]"
        );

        const signedUrls = await Promise.all(
          generalRecordArray.map(async (key) => {
            const signedUrl = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${key}`;
            return signedUrl;
          })
        );

        enrichedPatient.general_record = signedUrls;
      } catch (err) {
        console.error("Error parsing general_record:", err);
        patient.general_record_files = [];
      }

      enrichedPatient.patientFiles = await Promise.all(
        patientFiles.map(async (f) => ({
          ...f,
          file_name: f.file_name
            ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${f.file_name}`
            : null,
        }))
      );

      return sendResponse(
        res,
        200,
        "Patient fetched successfully",
        true,
        enrichedPatient
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllPatientsForDoctor = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Check if user is doctor or employee
      if (!req.user) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      let doctorId: number;

      if (req.user.role === UserRole.DOCTOR) {
        // If user is a doctor, use their ID
        doctorId = req.user.id;
      } else if (req.user.role === UserRole.EMPLOYEE) {
        // If user is an employee, find their supervisor (doctor)
        const employeeRelation = await db(TABLE.USER_EMPLOYEES)
          .where({
            employee_id: req.user.id,
            supervisor_type: "doctor",
            status: "active",
          })
          .first();

        if (!employeeRelation) {
          return sendResponse(
            res,
            403,
            "Employee not associated with any doctor or account is inactive",
            false
          );
        }

        doctorId = employeeRelation.supervisor_id;
      } else {
        return sendResponse(
          res,
          403,
          "Only doctors and employees can access patient list",
          false
        );
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      const baseUrl = process.env.BASE_URL;

      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      // Get total count for pagination
      const totalCountResult = await db(TABLE.PATIENTS)
        .where({ doctor_id: doctorId })
        .count({ count: "*" })
        .first();

      const total = Number(totalCountResult?.count || 0);
      const totalPages = Math.ceil(total / limit);

      const patients = await db(TABLE.PATIENTS)
        .where({ doctor_id: doctorId })
        .orderBy("created_at", "desc")
        .limit(limit)
        .offset(offset);

      const enrichedPatients = await Promise.all(
        patients.map(async (patient) => {
          let planName = null;
          if (patient.plan_id) {
            const plan = await db(TABLE.PLANS)
              .where({ id: patient.plan_id })
              .first();
            if (plan) planName = plan.name;
          }

          let clinicalConditions: string[] | null = null;
          if (patient.clinical_conditions) {
            try {
              clinicalConditions = JSON.parse(patient.clinical_conditions);
            } catch {
              clinicalConditions = null;
            }
          }

          const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patient.id })
            .orderBy("version_number", "desc")
            .first();
          const patientStatus = latestVersion ? latestVersion.status : null;

          const enriched = {
            ...patient,
            plan_name: planName,
            clinical_conditions: clinicalConditions,
            status: patientStatus,
          };

          for (const field of fileFields) {
            if (patient[field]) {
              const signedUrl = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${patient[field]}`;
              enriched[field] = signedUrl;
            }
          }

          try {
            const generalRecordArray: string[] = JSON.parse(
              patient.general_record || "[]"
            );

            const generalRecordSignedUrls = await Promise.all(
              generalRecordArray.map(async (key) => {
                const signedUrl = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${key}`;
                return signedUrl;
              })
            );

            enriched.general_record = generalRecordSignedUrls;
          } catch (err) {
            console.error("Error parsing general_record:", err);
            enriched.general_record_files = [];
          }

          return enriched;
        })
      );

      return sendResponse(res, 200, "Patients fetched successfully", true, {
        data: enrichedPatients,
        pagination: {
          total,
          page,
          limit,
          totalPages,
        },
      });
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const uploadCbctFile = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if user is doctor or employee
    if (!req.user) {
      return sendResponse(res, 401, "Unauthorized", false);
    }

    let doctorId: number;

    if (req.user.role === UserRole.DOCTOR) {
      // If user is a doctor, use their ID
      doctorId = req.user.id;
    } else if (req.user.role === UserRole.EMPLOYEE) {
      // If user is an employee, find their supervisor (doctor)
      const employeeRelation = await db(TABLE.USER_EMPLOYEES)
        .where({
          employee_id: req.user.id,
          supervisor_type: "doctor",
          status: "active",
        })
        .first();

      if (!employeeRelation) {
        return sendResponse(
          res,
          403,
          "Employee not associated with any doctor or account is inactive",
          false
        );
      }

      doctorId = employeeRelation.supervisor_id;
    } else {
      return sendResponse(
        res,
        403,
        "Only doctors and employees can upload CBCT files",
        false
      );
    }

    const { patientId } = req.params;
    const { reason } = req.body; // Get reason from request body

    // Verify patient exists and belongs to doctor
    const patient = await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: doctorId })
      .first();

    if (!patient) {
      return sendResponse(res, 404, "Patient not found or unauthorized", false);
    }
    // Check file exists
    if (!req.files) {
      return sendResponse(res, 400, "CBCT file is required", false);
    }
    let fileUrl;
    let signatureUrl;
    const file = (req.files as { [key: string]: Express.Multer.File[] })[
        "cbctFile"
      ][0];
    if (file) {
      try {
        const uploadResult = await uploadToOSS(
          file.buffer,
          file.originalname,
          file.mimetype,
          "cbctFiles"
        );
        if (!uploadResult) {
          return sendResponse(
            res,
            500,
            "Failed to upload profile image",
            false
          );
        }

        fileUrl = uploadResult.key;

        fileUrl = uploadResult.key;
        if (patient.file_name) {
          await deleteFromOSS(patient.file_name);
        }
        signatureUrl = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${fileUrl}`;
        console.log("signatureUrl:", signatureUrl);
      } catch (err) {
        console.error("Error uploading image:", err);
        return sendResponse(res, 500, "Failed to upload profile image", false);
      }
    }

    // Save to CBCT table with reason
    const [cbctFile] = await db("patient_files")
      .insert({
        patient_id: patientId,
        file_name: fileUrl,
        reason: reason || null, // Store reason if provided
      })
      .returning("*");

    sendResponse(res, 201, "CBCT uploaded successfully", true, {
      ...cbctFile,
      file_url: signatureUrl,
    });
  }
);

export const uploadRefinements = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if user is doctor or employee
    if (!req.user) {
      return sendResponse(res, 401, "Unauthorized", false);
    }

    let doctorId: number;

    if (req.user.role === UserRole.DOCTOR) {
      // If user is a doctor, use their ID
      doctorId = req.user.id;
    } else if (req.user.role === UserRole.EMPLOYEE) {
      // If user is an employee, find their supervisor (doctor)
      const employeeRelation = await db(TABLE.USER_EMPLOYEES)
        .where({
          employee_id: req.user.id,
          supervisor_type: "doctor",
          status: "active",
        })
        .first();

      if (!employeeRelation) {
        return sendResponse(
          res,
          403,
          "Employee not associated with any doctor or account is inactive",
          false
        );
      }

      doctorId = employeeRelation.supervisor_id;
    } else {
      return sendResponse(
        res,
        403,
        "Only doctors and employees can upload refinements",
        false
      );
    }

    const patientId = Number(req.params.patientId);
    const { refinementDetails } = req.body;

    // Verify patient
    const patient = await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: doctorId })
      .first();
    if (!patient) {
      return sendResponse(res, 404, "Patient not found or unauthorized", false);
    }

    // Check required files
    if (
      !req.files ||
      !(req.files as any).upperImpression ||
      !(req.files as any).lowerImpression
    ) {
      return sendResponse(
        res,
        400,
        "Both upperImpression and lowerImpression files are required",
        false
      );
    }

    // Cast files
    const files = req.files as unknown as Record<string, Express.Multer.File[]>;
    type FileField = {
      key: keyof typeof files;
      folder: string;
    };

    const fileFields: FileField[] = [
      { key: "upperImpression", folder: "impressions" },
      { key: "lowerImpression", folder: "impressions" },
      { key: "profileRepose", folder: "refinementImages" },
      { key: "buccalRight", folder: "refinementImages" },
      { key: "buccalLeft", folder: "refinementImages" },
      { key: "frontalRepose", folder: "refinementImages" },
      { key: "frontalSmiling", folder: "refinementImages" },
      { key: "labialAnterior", folder: "refinementImages" },
      { key: "occlussalLower", folder: "refinementImages" },
      { key: "occlussalUpper", folder: "refinementImages" },
      { key: "radioGraph1", folder: "radioGraphs" },
      { key: "radioGraph2", folder: "radioGraphs" },
    ];

    // Upload files and store OSS keys
    const uploadedFileMap: Record<string, string> = {};

    for (const { key, folder } of fileFields) {
      const file = files[key]?.[0];
      if (file && file.buffer && file.originalname && file.mimetype) {
        const uploaded = await uploadToOSS(
          file.buffer,
          file.originalname,
          file.mimetype,
          folder
        );
        if (uploaded?.key) {
          uploadedFileMap[key] = uploaded.key;
        }
      }
    }

    // Build payload with OSS keys
    const payload: any = {
      patient_id: patientId,
      refinement_details: refinementDetails || null,
      upper_impression: uploadedFileMap.upperImpression || null,
      lower_impression: uploadedFileMap.lowerImpression || null,
    };

    const optionalFields = [
      "profileRepose",
      "buccalRight",
      "buccalLeft",
      "frontalRepose",
      "frontalSmiling",
      "labialAnterior",
      "occlussalLower",
      "occlussalUpper",
      "radioGraph1",
      "radioGraph2",
    ];

    for (const field of optionalFields) {
      if (uploadedFileMap[field]) {
        payload[field] = uploadedFileMap[field];
      }
    }

    // Insert into DB
    const [record] = await db("refinements_aligner")
      .insert(payload)
      .returning("*");

    return sendResponse(res, 201, "Refinements uploaded", true, record);
  }
);

export const createAlignerReplacement = asyncHandler(
  async (req: Request, res: Response) => {
    const { patient_id, replacement_data } = req.body;

    // Field-specific validation
    if (!patient_id) {
      return res.status(400).json({
        success: false,
        field: "patient_id",
        message: "Patient ID is required.",
      });
    }

    if (!replacement_data) {
      return res.status(400).json({
        success: false,
        field: "replacement_data",
        message: "Replacement note is required.",
      });
    }

    // Insert into database
    const [inserted] = await db("aligner_replacements")
      .insert({
        patient_id,
        replacement: replacement_data,
      })
      .returning("id");

    res.status(201).json({
      success: true,
      message: "Aligner replacement added",
      patient_id,
      replacement_data,
    });
  }
);

export const requestRetainer = asyncHandler(
  async (req: Request, res: Response) => {
    const { patient_id, other_details, mode } = req.body;

    // 1) Validate
    if (!patient_id || !["upload", "use_last"].includes(mode)) {
      return sendResponse(res, 400, "Invalid payload", false);
    }

    // 2) MODE = use_last → fetch latest row
    if (mode === "use_last") {
      const last = await db("four_d_graphy_retainers")
        .where({ patient_id })
        .orderBy("created_at", "desc")
        .first();

      if (!last) {
        return sendResponse(
          res,
          404,
          "No previous retainer scans found",
          false
        );
      }
      return sendResponse(res, 200, "Last scans fetched", true, last);
    }

    // 3) MODE = upload → read files from req.files
    const files = req.files as unknown as Record<string, Express.Multer.File[]>;

    type FileField = {
      key: keyof typeof files;
      folder: string;
    };

    const fileFields: FileField[] = [
      { key: "stlFile1", folder: "stlFiles" },
      { key: "stlFile2", folder: "stlFiles" },
      { key: "profileRepose", folder: "retainerImages" },
      { key: "buccalRight", folder: "retainerImages" },
      { key: "buccalLeft", folder: "retainerImages" },
      { key: "frontalRepose", folder: "retainerImages" },
      { key: "frontalSmiling", folder: "retainerImages" },
      { key: "labialAnterior", folder: "retainerImages" },
      { key: "occlussalLower", folder: "retainerImages" },
      { key: "occlussalUpper", folder: "retainerImages" },
    ];

    const uploadResults: (null | { key: string; url: string })[] = [];

    for (let i = 0; i < fileFields.length; i++) {
      const { key, folder } = fileFields[i];
      const file = files[key]?.[0];

      if (file && file.buffer && file.originalname && file.mimetype) {
        const uploaded = await uploadToOSS(
          file.buffer,
          file.originalname,
          file.mimetype,
          folder
        );
        uploadResults.push(uploaded);
      } else {
        uploadResults.push(null);
      }
    }

    // 4) Insert new row (cbct and radiographs removed)
    const [inserted] = await db("four_d_graphy_retainers")
      .insert({
        patient_id,
        other_details: other_details || null,
        stl_file1: uploadResults[0]?.key,
        stl_file2: uploadResults[1]?.key,
        profile_repose: uploadResults[2]?.key,
        buccal_right: uploadResults[3]?.key,
        buccal_left: uploadResults[4]?.key,
        frontal_repose: uploadResults[5]?.key,
        frontal_smiling: uploadResults[6]?.key,
        labial_anterior: uploadResults[7]?.key,
        occlusal_lower: uploadResults[8]?.key,
        occlusal_upper: uploadResults[9]?.key,
      })
      .returning("*");

    return sendResponse(res, 201, "New scans saved", true, inserted);
  }
);
export const reviewSharedLinkVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // 1) Only doctors can review
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can review shared versions",
          false
        );
      }

      // 2) Extract versionId
      const { versionId } = req.params;
      if (!versionId) {
        return sendResponse(res, 400, "versionId is required", false);
      }

      // 3) Extract action + reason
      const { action, reason } = req.body;
      if (!["approve", "reject"].includes(action)) {
        return sendResponse(
          res,
          400,
          "Action must be 'approve' or 'reject'",
          false
        );
      }

      // 4) Fetch version
      const version = await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .first();
      if (!version) {
        return sendResponse(res, 404, "Version not found", false);
      }

      // 5) Only versions shared by specialist can be reviewed by doctor
      if (version.status !== "approved_by_specialist") {
        return sendResponse(
          res,
          400,
          "Only shared link versions can be reviewed by doctor",
          false
        );
      }

      // === Approve
      if (action === "approve") {
        await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: versionId })
          .update({
            status: "approved_by_doctor",
            approval_reason: reason || null,
          });

        return sendResponse(res, 200, "Version approved by doctor", true);
      }

      // === Reject
      if (!reason) {
        return sendResponse(res, 400, "Rejection reason is required", false);
      }

      await db(TABLE.PATIENTS_VERSIONS).where({ id: versionId }).update({
        status: "rejected_by_doctor",
        rejection_reason: reason,
      });

      return sendResponse(res, 200, "Version rejected by doctor", true);
    } catch (error: any) {
      console.error("reviewSharedLinkVersion error:", error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);
export const getAllPlans = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const page = parseInt((req.query.page as string) || "1", 10);
    const limit = parseInt((req.query.limit as string) || "10", 10);
    const data = await getAllPlansData(page, limit);
    sendResponse(res, 200, "Plans fetched successfully", true, data);
  }
);
export const getPlanById = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const plan = await getPlanByIdData(id);

    if (!plan) {
      sendResponse(res, 404, "Plan not found", false);
      return;
    }
    sendResponse(res, 200, "Plan fetched successfully", true, plan);
  }
);

// Get the latest specialist for a patient (for chat, etc.)
export const getSpecialistForPatient = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const patientId = Number(req.params.id);
      if (!patientId) {
        return sendResponse(res, 400, "Patient ID is required", false);
      }

      // Find the latest patient_versions row for this patient where specialist_id is not null
      const latestVersionWithSpecialist = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .whereNotNull("specialist_id")
        .orderBy("version_number", "desc")
        .first();

      if (!latestVersionWithSpecialist) {
        return sendResponse(
          res,
          404,
          "No specialist found for this patient",
          false
        );
      }

      // Ab specialist_id se specialist ka user record nikalain
      const specialist = await db(TABLE.USERS)
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
        .where(`${TABLE.USERS}.id`, latestVersionWithSpecialist.specialist_id)
        .where(`${TABLE.ROLES}.role_name`, "specialist")
        .select(
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.USERS}.is_active`,
          `${TABLE.USERS}.is_verified`,
          `${TABLE.USERS}.created_at`,
          `${TABLE.USERS}.updated_at`
        )
        .first();

      if (!specialist) {
        return sendResponse(res, 404, "Specialist user not found", false);
      }

      return sendResponse(res, 200, "Specialist found", true, specialist);
    } catch (error: any) {
      console.error(error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);

export const updatePatientStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { patientId } = req.params;
      const { status, reason } = req.body;

      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can update patient status",
          false
        );
      }

      // Verify patient belongs to doctor
      const patient = await db(TABLE.PATIENTS)
        .where({ id: patientId, doctor_id: req.user.id })
        .first();

      if (!patient) {
        return sendResponse(
          res,
          404,
          "Patient not found or doesn't belong to you",
          false
        );
      }

      // Get the latest version
      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "desc")
        .first();

      if (latestVersion) {
        // Update the status
        await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: latestVersion.id })
          .update({
            status,
            ...(reason && { approval_reason: reason }),
          });

        // Find the associated specialist
        let specialist = null;
        if (latestVersion.specialist_id) {
          specialist = await db(TABLE.USERS)
            .where({ id: latestVersion.specialist_id })
            .first();
        }

        // If no specific specialist, find any active specialist
        if (!specialist) {
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.ROLES}.role_name`, "specialist")
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        // Send notification to specialist
        if (specialist) {
          const [notification] = await db(TABLE.NOTIFICATIONS)
            .insert({
              receiver_id: specialist.id,
              user_id: req.user.id,
              title: "Patient Status Updated",
              message: `Patient ${patient.first_name} ${patient.last_name} status changed to ${status}`,
              details: JSON.stringify({
                type: "patient_status_updated",
                doctor_id: req.user.id,
                patient_id: patientId,
                patient_name: `${patient.first_name} ${patient.last_name}`,
                doctor_name: `${req.user.first_name} ${req.user.last_name}`,
                old_status: latestVersion.status,
                new_status: status,
                reason: reason || null,
              }),
            })
            .returning("*");

          // Send real-time notification
          const io = getIO();
          const userSockets = users.find(
            (u) => u.userId === String(specialist.id)
          );
          if (userSockets) {
            const unreadCount = await db(TABLE.NOTIFICATIONS)
              .where({ receiver_id: specialist.id, is_read: false })
              .count("id as count")
              .first()
              .then((r: any) => parseInt(r.count) || 0);

            userSockets.socketIds.forEach((socketId) =>
              io.to(socketId).emit("notification", {
                notifications: [notification],
                unread_count: unreadCount,
              })
            );
          }
        }
      }

      return sendResponse(
        res,
        200,
        "Patient status updated successfully",
        true,
        { patientId, status, reason }
      );
    } catch (error: any) {
      console.error("updatePatientStatus error:", error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);


export const createUpdatesVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { patientId } = req.params;
      const { status, reason, shared_link } = req.body;
      if (!req.user) {
        return sendResponse(res, 401, "Unauthorized", false);
      }
      if ( req.user?.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialist can update patient status",
          false
        );
      }
      if (!patientId || !status) {
        return sendResponse(
          res,
          400,
          "Patient ID and status are required",
          false
        );
      }
      if (!["in_working", "completed"].includes(status)) {
        return sendResponse(
          res,
          400,
          "Invalid status. Must be 'in_working' or 'completed'",
          false
        );
      }
     

      // Verify patient belongs to doctor
      const patient = await db(TABLE.PATIENTS)
        .where({ id: patientId })
        .first();
    

      if (!patient) {
        return sendResponse(
          res,
          404,
          "Patient not found",
          false
        );
      }

      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "desc")
        .first();
    
      if (latestVersion) {
          await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: latestVersion.id })
         .update({
             is_latest_version: false,  
          });
        }

      const newVersion=await db(TABLE.PATIENTS_VERSIONS)
      .insert({
        patient_id: patientId,
        status,
        approval_reason: reason || null,
        shared_link: shared_link || null,
        specialist_id: req.user.id,
        created_by: req.user.id,
        version_number: latestVersion ? latestVersion.version_number + 1 : 1,
        title: `Initial V${latestVersion ? latestVersion.version_number + 1 : 1}`,
        is_latest_version: true,
      }).returning("*");


      // Find the associated doctor
      const doctor = await db(TABLE.USERS)
        
        .where({ id: patient.doctor_id })
        .first();
      // Send notification to doctor
      if (doctor) {
        const [notification] = await db(TABLE.NOTIFICATIONS)
          .insert({
            receiver_id: doctor.id,
            user_id: req.user.id,
            title: "Patient Status Updated",
            message: `Patient ${patient.first_name} ${patient.last_name} status changed to ${status}`,
            details: JSON.stringify({
              type: "patient_status_updated",
              specialist_id: req.user.id,
              patient_id: patientId,
              patient_name: `${patient.first_name} ${patient.last_name}`,
              specialist_name: `${req.user.first_name} ${req.user.last_name}`,
              old_status: latestVersion ? latestVersion.status : null,
              new_status: status,
              reason: reason || null,
            }),
          })
          .returning("*");

        // Send real-time notification
        const io = getIO();
        const userSockets = users.find(
          (u) => u.userId === String(doctor.id)
        );
        if (userSockets) {
          const unreadCount = await db(TABLE.NOTIFICATIONS)
            .where({ receiver_id: doctor.id, is_read: false })
            .count("id as count")
            .first()
            .then((r: any) => parseInt(r.count) || 0);

          userSockets.socketIds.forEach((socketId) =>
            io.to(socketId).emit("notification", {
              notifications: [notification],
              unread_count: unreadCount,
            })
          );
        }
      }
      const responseData = {
        ...newVersion[0],
        status: newVersion[0].status=="completed" ? "shipped" : "in_working",
      }

      return sendResponse(
        res,
        200,
        "Patient status updated successfully",
        true,
        responseData
      );
    } catch (error: any) {
      console.error("updatePatientStatus error:", error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);
export const statusUpdateById = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const doctorId = req.user?.id;
    if(req.user?.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can update Patient status", false);
      return;
    }

    const { status } = req.body;
    if (!status) {
      sendResponse(res, 400, "Status is required", false);
      return;
    }
    try {
      const patient = await db(TABLE.PATIENTS).where({ id }).first();
      if (!patient) {
        sendResponse(res, 404, "Patient not found", false);
        return;
      }

      if (patient.doctor_id !== doctorId) {
        sendResponse(res, 403, "You are not associated with this patient", false);
        return;
      }

      const updatedPatientData = await db(TABLE.PATIENTS)
      .where({ id })
      .update({ is_active: status === "active" }) 
      .returning("*");

      if (updatedPatientData.length === 0) {
        sendResponse(res, 404, "Patient not found", false);
        return;
      }

      sendResponse(
        res,
        200,
        "Patient status updated successfully",
        true,
        updatedPatientData[0]
      );
    } catch (error: any) {
      console.error("Error updating Patient status:", error);
      sendResponse(res, 500, error.message || "Internal server error", false);
    }       
  }
);