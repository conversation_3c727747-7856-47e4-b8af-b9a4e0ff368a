import OSS from "ali-oss";

const requiredEnvVars = [
  "REGION",
  "ACCESS_KEY_ID",
  "ACCESS_KEY_SECRET",
  "BUCKET_NAME",
];
const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

if (missingVars.length > 0) {
  throw new Error(
    `Missing required environment variables: ${missingVars.join(", ")}`
  );
}

const client = new OSS({
  region: process.env.REGION!,
  accessKeyId: process.env.ACCESS_KEY_ID!,
  accessKeySecret: process.env.ACCESS_KEY_SECRET!,
  bucket: process.env.BUCKET_NAME!,
  endpoint: "https://oss-me-central-1.aliyuncs.com",
});

const testConnection = async () => {
  try {
    await client.getBucketInfo(process.env.BUCKET_NAME!);
    console.log("✅ OSS connection successful");
  } catch (error) {
    console.error("❌ OSS connection failed:", error);
    throw error;
  }
};

testConnection().catch(console.error);

export default client;
