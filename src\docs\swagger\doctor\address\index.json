{"paths": {"/doctor/addresses": {"post": {"tags": ["Doctor Management"], "summary": "Add new address for doctor", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["clinic_name", "street_address", "city"], "properties": {"clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}}}}}}, "responses": {"201": {"description": "Address added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Address added successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "address_type": {"type": "string", "example": "bill_to"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation error"}}}}}}, "403": {"description": "Unauthorized - Only doctors can add addresses", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized - Only doctors can add addresses"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized - Invalid or missing token"}}}}}}}}, "get": {"tags": ["Doctor Management"], "summary": "Get all addresses for doctor", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Addresses retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Addresses retrieved"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "address_type": {"type": "string", "example": "bill_to"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}}, "403": {"description": "Unauthorized - Only doctors can view addresses", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized - Only doctors can view addresses"}}}}}}}}}, "/doctor/addresses/{addressId}": {"put": {"tags": ["Doctor Management"], "summary": "Update doctor address", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "addressId", "required": true, "schema": {"type": "integer"}, "description": "ID of address to update"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["clinic_name", "street_address", "city"], "properties": {"clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}}}}}}, "responses": {"200": {"description": "Address updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Address updated successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "address_type": {"type": "string", "example": "bill_to"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}, "404": {"description": "Address not found or doesn't belong to doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Address not found or doesn't belong to doctor"}}}}}}}}, "delete": {"tags": ["Doctor Management"], "summary": "Delete doctor address", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "addressId", "required": true, "schema": {"type": "integer"}, "description": "ID of address to delete"}], "responses": {"200": {"description": "Address deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Address deleted successfully"}}}}}}, "404": {"description": "Address not found or doesn't belong to doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Address not found or doesn't belong to doctor"}}}}}}}}, "get": {"tags": ["Doctor Management"], "summary": "Get a doctor address by ID", "description": "Fetch a single address for the doctor by its unique ID. Returns all address details including address_type.", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "addressId", "required": true, "schema": {"type": "integer"}, "description": "ID of the address to fetch"}], "responses": {"200": {"description": "Address fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Address fetched successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "address_type": {"type": "string", "example": "bill_to"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}, "404": {"description": "Address not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Address not found"}}}}}}}}}}, "components": {"schemas": {"Address": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "address_type": {"type": "string", "example": "bill_to", "description": "Type of address: bill_to or ship_to"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "AddressInput": {"type": "object", "required": ["clinic_name", "street_address", "city"], "properties": {"clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}}}, "AddressResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Address"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}}}}