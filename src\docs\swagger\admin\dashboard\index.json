{"openapi": "3.0.0", "info": {"title": "Admin Dashboard API", "version": "1.0.0", "description": "Swagger documentation for Admin Dashboard related endpoints."}, "tags": [{"name": "Admin Dashboard", "description": "Endpoints for admin dashboard operations."}], "paths": {"/admin/dashboard": {"get": {"tags": ["Admin Dashboard"], "summary": "Get dashboard statistics", "description": "Returns counts of doctors, patients, and employees.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dashboard stats fetched successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"doctors": {"type": "integer"}, "patients": {"type": "integer"}, "employees": {"type": "integer"}}}}}, "example": {"success": true, "message": "Dashboard stats fetched", "data": {"doctors": 10, "patients": 50, "employees": 5}}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}}, "example": {"success": false, "message": "Internal server error"}}}}}}}}}