import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.PATIENTS, (table) => {
    table.string("country").notNullable().defaultTo("Saudia");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.PATIENTS, (table) => {
    table.dropColumn("country");
  });
}
