services:
  postgres:
    image: postgres:latest
    container_name: orthodontic-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: orthodontic_db
    ports:
      - "5433:5432"  # Changed from 5432 to 5433
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  orthodontic-backend:
    build: .
    container_name: orthodontic-backend
    restart: no
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DB_HOST: postgres
      DB_PORT: 5432  # Changed to internal port
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: orthodontic_db
    ports:
      - "5004:5004"  # Changed to match your server port
    volumes:
      - .:/app
      - node_modules_cache:/app/node_modules
    working_dir: /app
    command: npx nodemon -L src/index.ts

volumes:
  postgres_data:
  node_modules_cache: