import db from "../config/db";
import { TABLE } from "../utils/Database/table";

export const getAllPlansData = async (page: number, limit: number,   sortOrder: "asc" | "desc" = "desc"
) => {
    const offset = (page - 1) * limit;

    const plans = await db(TABLE.PLANS)
      .select(
        "id",
        "name",
        "type",
        "duration_years",
        "expiration_date",
        "created_at",
        "updated_at"
      )
      .offset(offset)
      .limit(limit)
      .orderBy("created_at", sortOrder);

    const totalResult = (await db(TABLE.PLANS)
      .count("id as count")) as Array<{ count: string }>;
    const total = Number(totalResult[0].count);

    return {
      plans,
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    };
};

export const getPlanByIdData = async (id: string) => {
    const plan = await db(TABLE.PLANS)
      .select(
        "id",
        "name",
        "type",
        "duration_years",
        "expiration_date",
        "created_at",
        "updated_at"
      )
      .where({ id })
      .first();

    return plan;
};