import { Router } from "express";

import { storageData } from "../../utils/services/multer";
import {
  // reviewPatientFile,
  reviewPatientVersion,
  getAllPatientsForSpecialist,
  getPatientByIdForSpecialist,
  createEmployee,
  getEmployees,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
  updateEmployeeStatus,
} from "../../controller/specialist.controller";
import { authMiddleware } from "../../middlewares/authMiddleware";

const router = Router();
const upload = storageData("specialist");

// Employee management routes
router.post("/employees", authMiddleware, upload.none(), createEmployee);
router.get("/employees", authMiddleware, getEmployees);
router.get("/employee/:employeeId", authMiddleware, getEmployeeById);
router.put(
  "/employees/:employeeId",
  authMiddleware,
  upload.none(),
  updateEmployee
);
router.delete("/employees/:employeeId", authMiddleware, deleteEmployee);
router.patch(
  "/employees/:employeeId",
  authMiddleware,
  upload.none(),
  updateEmployeeStatus
);

// Patient management routes
// router.post(
//   "/patient-files/:patientFileId/review",
//   upload.single("rejection_pdf"),
//   reviewPatientFile
// );
router.post(
  "/patient-versions/:versionId/review",
  authMiddleware,
  upload.none(),
  reviewPatientVersion
);
router.get("/patients", authMiddleware, getAllPatientsForSpecialist);
router.get("/patient/:id", authMiddleware, getPatientByIdForSpecialist);
export default router;
