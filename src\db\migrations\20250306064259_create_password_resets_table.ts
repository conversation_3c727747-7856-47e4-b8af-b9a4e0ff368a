import { Knex } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(TABLE.PASSWORD_RESETS, (table) => {
    table.increments("id").primary();
    table.string("email").notNullable().index();
    table.string("token").notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTableIfExists(TABLE.PASSWORD_RESETS);
}
