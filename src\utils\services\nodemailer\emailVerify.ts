import { sendEmail } from "./index";
import { Response } from "express";
import { response } from "../../response";
import { User } from "../../types/auth";
import { generateOTP } from "../../helperFunctions/otpGenerator";
import db from "../../../config/db";
import { TABLE } from "../../Database/table";

export const sendVerificationEmail = async (
  user: User,
  res: Response
): Promise<void> => {
  try {
    const otp = await generateOTP();

    // Save OTP for verification
    await db(TABLE.PASSWORD_RESETS).insert({
      email: user.email,
      token: otp,
      created_at: new Date(),
    });

    const htmlContent = generateVerificationEmailTemplate(user, otp);

    await sendEmail({
      to: user.email,
      subject: "Kitchen Konnect - Verify Your Email",
      html: htmlContent,
      senderName: "Kitchen Konnect Team",
      senderEmail: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
    });
    console.log("Verification email sent successfully!");

    response(
      res,
      200,
      "Your account is not verified. Verification email sent. Please check your email."
    );
  } catch (error) {
    console.error("Error sending verification email:", error);
    res.status(500).json({
      message: "An error occurred while sending the verification email.",
    });
  }
};

const generateVerificationEmailTemplate = (user: User, otp: string) => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f4f4f4; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 20px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); text-align: center; }
        .header { font-size: 24px; font-weight: bold; color: #5e9b6d; margin-bottom: 20px; }
        .content { font-size: 16px; line-height: 1.5; }
        .otp { font-size: 22px; font-weight: bold; background: #e3fcef; padding: 10px; border-radius: 5px; display: inline-block; }
        .footer { font-size: 14px; color: #777; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Verify Your Email</div>
        <div class="content">
            <p>Hi ${user?.first_name} ${user?.last_name},</p>
            <p>Your email verification OTP is:</p>
            <p class="otp">${otp}</p>
            <p>This OTP is valid for 10 minutes. If you did not request this, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>Kitchen Konnect Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
