// migrations/20250630_add_user_uuid_to_users.ts
import type { Knex } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.USERS, (table) => {
    // remove any old uuid/default; add plain string column
    table.string("user_uuid").unique().nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.USERS, (table) => {
    table.dropColumn("user_uuid");
  });
}
