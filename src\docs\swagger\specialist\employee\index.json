{"paths": {"/specialist/employees": {"post": {"summary": "Create a new employee under a specialist", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe", "description": "Optional. Must be unique if provided."}, "salutation": {"type": "string", "example": "Dr."}, "practice_phone_number": {"type": "string", "example": "+1234567890"}, "mobile": {"type": "string", "example": "+1234567890"}, "profession": {"type": "string", "example": "Dental Assistant"}}, "required": ["first_name", "last_name", "email", "practice_phone_number"]}}}}, "responses": {"201": {"description": "Employee created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employee created successfully and credentials sent via email"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "role_id": {"type": "integer", "example": 3}}}}}}}}, "400": {"description": "Bad request - validation error or duplicate email/username", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Email already exists"}}}}}}, "403": {"description": "Forbidden - User is not a specialist", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only specialists can create employees"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}, "get": {"summary": "Get all employees under a specialist", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "Number of items per page"}], "responses": {"200": {"description": "Employees retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employees retrieved successfully"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "role": {"type": "string", "example": "employee"}, "status": {"type": "string", "example": "active"}}}}, "currentPage": {"type": "integer", "example": 1}, "totalItems": {"type": "integer", "example": 10}, "totalPages": {"type": "integer", "example": 1}}}}}}}}, "403": {"description": "Forbidden - User is not a specialist", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only specialists can view their employees"}}}}}}}}}, "/specialist/employee/{employeeId}": {"get": {"summary": "Get employee by ID", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Employee fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employee fetched successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}}}}}}}}}}}, "/specialist/employees/{employeeId}": {"put": {"summary": "Update an employee's information", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "salutation": {"type": "string", "example": "Dr."}, "practice_phone_number": {"type": "string", "example": "+1234567890"}, "mobile": {"type": "string", "example": "+1234567890"}, "profession": {"type": "string", "example": "Dental Assistant"}}}}}}, "responses": {"200": {"description": "Employee updated successfully"}}}, "delete": {"summary": "Delete an employee", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Employee deleted successfully"}}}, "patch": {"summary": "Update employee status", "tags": ["Specialist Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "inactive"], "example": "active"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Employee status updated successfully"}}}}}}