const axios = require('axios');

async function testCreateUser() {
  try {
    console.log("🧪 Testing auth createUser endpoint...");

    // First, let's get an admin token by logging in
    const loginResponse = await axios.post('http://localhost:5008/api/v1/auth/login-admin', {
      email: '<EMAIL>',
      password: 'Admin@123'
    });

    if (!loginResponse.data.success) {
      console.error("❌ Failed to login as admin:", loginResponse.data);
      return;
    }

    const adminToken = loginResponse.data.data.token;
    console.log("✅ Admin login successful");

    // Now test creating a user
    const testUserData = {
      first_name: "Test",
      last_name: "User",
      email: `test_${Date.now()}@example.com`,
      role_id: "2", // Assuming role_id 2 exists (admin role)
      username: `testuser_${Date.now()}`,
      password: "TestPassword123"
    };

    console.log("🧩 Creating user with data:", testUserData);

    const createUserResponse = await axios.post(
      'http://localhost:5008/api/v1/auth/register-user',
      testUserData,
      {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log("🔍 Create user response:", createUserResponse.data);

    if (createUserResponse.data.success) {
      const userData = createUserResponse.data.data;
      console.log("✅ User created successfully!");
      console.log("🎯 User data:", userData);
      
      if (userData.user_uuid) {
        console.log("✅ user_uuid is present:", userData.user_uuid);
      } else {
        console.log("❌ user_uuid is missing from response");
      }
    } else {
      console.log("❌ User creation failed:", createUserResponse.data);
    }

  } catch (error) {
    console.error("❌ Error during test:", error.response?.data || error.message);
  }
}

// Run the test
testCreateUser();
