import type { Server, Socket } from "socket.io";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import {
  uploadImageToOSS,
  compressImage,
} from "../../utils/services/oss/imageUploadHelper";
import { appLogger, errorLogger } from "../../config/logger";

// Define max file size (10 MB for images, 50 MB for other files)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

// Define allowed file types
const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "image/webp",
];

const ALLOWED_FILE_TYPES = [
  ...ALLOWED_IMAGE_TYPES,
  "application/zip",
  "application/x-zip",
  "application/x-zip-compressed",
  "application/octet-stream",
  "application/pdf",
];

export const setupChat = (io: Server, socket: Socket) => {
  console.log(`Chat controller setup for socket: ${socket.id}`);
  
  const sendError = (msg: string) => {
    console.log(`Sending error to ${socket.id}: ${msg}`);
    socket.emit("responseError", msg);
    errorLogger.error("Chat error", { message: msg, socketId: socket.id });
  };

  const sendSuccess = (event: string, data: any) => {
    console.log(`Sending ${event} to ${socket.id}:`, data);
    socket.emit(event, data);
  };

  // Fetch Conversations (patient-specific)
  socket.on("getConversations", async ({ userId, patientId }) => {
    if (!userId || !patientId)
      return sendError("User ID and Patient ID are required");

    try {
      const conversations = await db
        .select([
          "c.id",
          "c.sender_id",
          "c.receiver_id",
          "c.patient_id",
          "c.message",
          "c.is_read",
          "c.created_at",
          "u.id as user_id",
          "u.first_name",
          "u.last_name",
          db.raw(`
            CASE
              WHEN u.profile_image IS NOT NULL
              THEN CONCAT('https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/', u.profile_image)
              ELSE NULL
            END as profile_image
          `),
          db.raw(
            `(SELECT COUNT(*)
                FROM ${TABLE.CHATS} as sub
                WHERE 
                  sub.is_read = false AND
                  sub.patient_id = ? AND
                  (
                    (LEAST(sub.sender_id, sub.receiver_id) = LEAST(c.sender_id, c.receiver_id)) AND
                    (GREATEST(sub.sender_id, sub.receiver_id) = GREATEST(c.sender_id, c.receiver_id))
                  ) AND
                  sub.receiver_id = ?
              ) as unread_count
            `,
            [patientId, userId]
          ),
        ])
        .from(function (this: any) {
          this.select("*")
            .from(TABLE.CHATS)
            .where("patient_id", patientId)
            .andWhere(function (this: any) {
              this.where("sender_id", userId).orWhere("receiver_id", userId);
            })
            .as("c");
        })
        .join({ u: TABLE.USERS }, function () {
          this.on(function () {
            this.on("c.sender_id", db.raw("?", [userId])).andOn(
              "u.id",
              "=",
              "c.receiver_id"
            );
          }).orOn(function () {
            this.on("c.receiver_id", db.raw("?", [userId])).andOn(
              "u.id",
              "=",
              "c.sender_id"
            );
          });
        })
        .distinctOn([
          db.raw("LEAST(c.sender_id, c.receiver_id)") as any,
          db.raw("GREATEST(c.sender_id, c.receiver_id)") as any,
        ]).orderByRaw(`
          LEAST(c.sender_id, c.receiver_id),
          GREATEST(c.sender_id, c.receiver_id),
          c.created_at DESC
        `);

      const formatted = conversations.map((conv: any) => ({
        message_id: conv.id,
        message: conv.message,
        created_at: conv.created_at,
        unread_count: conv.unread_count,
        user: {
          id: conv.user_id,
          name: `${conv.first_name} ${conv.last_name}`,
          profile_image: conv.profile_image,
        },
      }));

      socket.emit("conversationsList", formatted);
    } catch (error) {
      console.error("Fetch conversation error:", error);
      sendError("Failed to fetch conversations");
    }
  });

  // Fetch Messages (patient-specific)
  socket.on(
    "getConversationMessages",
    async ({ user_id, target_user_id, patient_id }) => {
      try {
        const messages = await db(`${TABLE.CHATS} as c`)
          .select(
            "c.id",
            "c.sender_id",
            "c.receiver_id",
            "c.patient_id",
            "c.message",
            db.raw(`
            CASE
              WHEN c.file_url IS NOT NULL
              THEN CONCAT('https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/', c.file_url)
              ELSE NULL
            END as file_url
          `),
            "c.file_name",
            "c.file_type",
            "c.file_size",
            "c.is_read",
            "c.created_at",
            "u.first_name",
            "u.last_name"
          )
          .leftJoin(`${TABLE.USERS} as u`, "c.sender_id", "u.id")
          .where("c.patient_id", patient_id)
          .andWhere(function () {
            this.where("c.sender_id", user_id)
              .andWhere("c.receiver_id", target_user_id)
              .orWhere("c.sender_id", target_user_id)
              .andWhere("c.receiver_id", user_id);
          })
          .orderBy("c.created_at", "asc");

        socket.emit("messagesList", messages);
      } catch (error) {
        console.error("Fetch messages error:", error);
        sendError("Failed to fetch conversation messages");
      }
    }
  );

  // Send Message with optimized image upload
  socket.on(
    "sendMessage",
    async (data: {
      sender_id: number;
      receiver_id: number;
      patient_id: number;
      message?: string;
      file_base64?: string;
      file_name?: string;
      file_type?: string;
      file_size?: number;
    }) => {
      console.log(`sendMessage received from ${socket.id}:`, {
        sender_id: data.sender_id,
        receiver_id: data.receiver_id,
        patient_id: data.patient_id,
        message: data.message,
        hasFile: !!data.file_base64
      });
      
      try {
        const {
          sender_id,
          receiver_id,
          patient_id,
          message = "",
          file_base64,
          file_name,
          file_type,
          file_size,
        } = data;


        // Validate file size based on type
        const isImage = file_type && ALLOWED_IMAGE_TYPES.includes(file_type);

        // Basic validation
        if (!sender_id || !receiver_id || !patient_id) {
          return sendError("sender_id, receiver_id, and patient_id are required");
        }

        if (!message && !file_base64) {
          return sendError("Either message or file is required");
        }

        // Validate file size

        if (file_size && file_size > MAX_FILE_SIZE) {
          return sendError(`File size exceeds the "50" MB limit`);
        }

        // Validate file type
        if (file_type && file_name) {
          const fileExtension = file_name.split(".").pop()?.toLowerCase();
          const isAllowedMimeType = ALLOWED_FILE_TYPES.includes(file_type);
          const isAllowed =
            isAllowedMimeType ||
            (file_type === "application/octet-stream" &&
              fileExtension === "zip");

          if (!isAllowed) {
            return sendError(
              `File type not allowed (${file_type}). Please upload an image, PDF, or ZIP file.`
            );
          }
        }

        let fileUrl: string | null = null;
        let actualFileSize = file_size;

        // Handle file upload to OSS
        if (file_base64 && file_name && file_type) {
          try {
            // Extract base64 data
            const base64Data = file_base64.replace(/^data:[^;]+;base64,/, "");
            let buffer = Buffer.from(base64Data, "base64");

            // Compress image if it's an image file
            if (isImage) {
              const compressionResult = await compressImage(buffer, file_type);
              // @ts-ignore
              buffer = compressionResult.buffer;
              actualFileSize = compressionResult.size;
            }

            // Upload to OSS
            const uploadResult = await uploadImageToOSS(
              buffer,
              file_name,
              file_type,
              `chat/${patient_id}`
            );

            fileUrl = uploadResult.key;
          } catch (uploadError: any) {
            return sendError("Failed to upload file. Please try again.");
          }
        }

        // Insert into DB
        console.log(`Inserting message into database...`);
        const [saved] = await db(TABLE.CHATS)
          .insert({
            sender_id,
            receiver_id,
            patient_id,
            message: message || null,
            file_url: fileUrl,
            file_name,
            file_type,
            file_size: actualFileSize,
          })
          .returning("*");

        console.log(`Message saved with ID: ${saved.id}`);

        // Fetch sender and receiver names from USERS table
        const users = await db(TABLE.USERS)
          .select("id", "first_name", "last_name")
          .whereIn("id", [sender_id, receiver_id]);

        let sender_name = "";
        let receiver_name = "";
        users.forEach(u => {
          if (u.id === sender_id) sender_name = `${u.first_name} ${u.last_name}`;
          if (u.id === receiver_id) receiver_name = `${u.first_name} ${u.last_name}`;
        });

        // Console log for verification
        console.log(`Sender Name: ${sender_name}, Receiver Name: ${receiver_name}`);

        // Build message object with names
        const messageWithNames = {
          ...saved,
          sender_name,
          receiver_name,
        };

        // Emit to both sender and receiver
        console.log(`Emitting newMessage to sender ${sender_id} and receiver ${receiver_id}`);
        socket.emit("newMessage", messageWithNames);
        io.to(`user_${receiver_id}`).emit("newMessage", messageWithNames);

        console.log(`Message sent successfully`);
      } catch (err) {
        console.error("Send message error:", err);

        sendError("Failed to send message");
      }
    }
  );

  // Mark Messages as Read
  socket.on(
    "markMessagesAsRead",
    async ({ user_id, sender_id, patient_id }) => {
      try {
        await db(TABLE.CHATS)
          .where("sender_id", sender_id)
          .andWhere("receiver_id", user_id)
          .andWhere("patient_id", patient_id)
          .andWhere("is_read", false)
          .update({ is_read: true });

        io.to(`user_${sender_id}`).emit("messagesMarkedAsRead", {
          readerId: user_id,
          senderId: sender_id,
          patientId: patient_id,
        });
      } catch (error) {
        console.error("Mark as read error:", error);
        sendError("Failed to mark messages as read");
      }
    }
  );

  // Delete Message
  socket.on(
    "deleteMessage",
    async ({ messageId, sender_id, receiver_id, patient_id }) => {
      try {
        const deleted = await db(TABLE.CHATS)
          .where({ id: messageId, sender_id, patient_id })
          .del();

        if (!deleted) return sendError("Message not found or unauthorized");

        socket.emit("messageDeleted", { messageId });
        io.to(`user_${receiver_id}`).emit("messageDeleted", { messageId });
      } catch (error) {
        console.error("Delete message error:", error);
        sendError("Failed to delete message");
      }
    }
  );
};
