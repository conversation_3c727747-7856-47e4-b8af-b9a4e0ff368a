import { sendEmail } from "./index";
import { Response } from "express";
import { response } from "../../response";
import { User } from "../../types/auth";
import { generateOTP } from "../../helperFunctions/otpGenerator";
import db from "../../../config/db";
import { TABLE } from "../../Database/table";

export const sendRegistrationEmail = async (
  user: User,
  res: Response
): Promise<void> => {
  const otp = await generateOTP();

  await db(TABLE.PASSWORD_RESETS).insert({
    email: user.email,
    token: otp,
  });

  const htmlContent = await registerUserVerificationEmailTemplate(user, otp);

  try {
    await sendEmail({
      to: user.email,
      subject: "Welcome to Kitchen Konnect - Verify Your Email",
      html: htmlContent,
      senderName: "Kitchen Konnect Team",
      senderEmail: process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
    });
    console.log("Registration email sent successfully!");

    response(
      res,
      201,
      "Registration email sent successfully. Please check your email."
    );
  } catch (error) {
    console.error("Error sending registration email:", error);
    res.status(500).json({
      message: "An error occurred while sending the registration email.",
    });
  }
};

const registerUserVerificationEmailTemplate = (user: User, otp: string) => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .header {
            font-size: 24px;
            font-weight: bold;
            color: #5e9b6d;
            margin-bottom: 20px;
        }
        .content {
            font-size: 16px;
            line-height: 1.5;
        }
        .otp {
            font-size: 22px;
            font-weight: bold;
            display: inline-block;
            margin: 20px 0;
        }
        .footer {
            font-size: 14px;
            color: #777;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Welcome to Kitchen Konnect!</div>
        <div class="content">
            <p>Hi ${user?.first_name} ${user?.last_name},</p>
            <p>Thank you for registering with <strong>Kitchen Konnect</strong>! We're excited to have you onboard.</p>
            <p>To complete your registration, please verify your email using the OTP below:</p>
            <p class="otp">${otp}</p>
            <p>If you did not register with Kitchen Konnect, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>Kitchen Konnect Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
