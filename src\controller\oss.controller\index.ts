import OSS from "../../config/oss-config";
import type { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import asyncHandler from "../../middlewares/trycatch";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import db from "../../config/db";
import { decryptTableName } from "../../utils/helperFunctions/decryption";

interface UploadResponse {
  message: string;
  data: {
    url: string;
    name: string;
    key: string;
    size: number;
    mimetype: string;
  };
}

interface GetResponse {
  url: string;
}

interface DeleteResponse {
  message: string;
}

// Extend Request to include Multer file
interface MulterRequest extends Request {
  file?: Express.Multer.File;
}

export const uploadImage = async (
  req: MulterRequest,
  res: Response<UploadResponse | { error: string }>
): Promise<void> => {
  try {
    if (!req.file) {
      res.status(400).json({
        error:
          "No file uploaded. Please ensure you're using the correct field name 'file' or 'image'.",
      });
      return;
    }
    if (req.file.size > 50 * 1024 * 1024) {
      res.status(400).json({ error: "File size exceeds 50MB limit" });
      return;
    }
    const timestamp = Date.now();
    const fileExtension = req.file.originalname.split(".").pop() || "bin";
    const fileName = `uploads-${timestamp}-${uuidv4()}.${fileExtension}`;
    // Upload to OSS
    const result = await OSS.put(fileName, req.file.buffer, {
      headers: {
        "Content-Type": req.file.mimetype,
      },
    });
    res.json({
      message: "File uploaded successfully",
      data: {
        url: result.url,
        name: result.name,
        key: fileName,
        size: req.file.size,
        mimetype: req.file.mimetype,
      },
    });
  } catch (error) {
    console.error("Upload error:", error);
    res.status(500).json({
      error:
        "Failed to upload file. Please check your OSS configuration and try again.",
    });
  }
};

export const getImage = async (
  req: Request<{ key: string }>,
  res: Response<GetResponse | { error: string }>
): Promise<void> => {
  try {
    const { key } = req.params;
    if (!key) {
      res.status(400).json({ error: "File key is required" });
      return;
    }
    // Decode the key in case it's URL encoded
    const decodedKey = decodeURIComponent(key);
    // Generate signed URL valid for 1 hour
    const url = await OSS.signatureUrl(decodedKey, { expires: 3600 });
    res.json({ url });
  } catch (error) {
    console.error("Get image error:", error);
    res.status(500).json({ error: "Failed to retrieve file" });
  }
};

export const deleteRecord = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { record_id, fieldName, tableName, fileKey } = req.body;
      const plainTableName = decryptTableName(tableName);

      if (!record_id || !fieldName || !plainTableName) {
        return sendResponse(res, 400, "Missing required parameters", false);
      }
      if (!req.user) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      if (fileKey) {
        const decodedKey = decodeURIComponent(fileKey);
        await OSS.delete(decodedKey);
        console.log("Image deleted from OSS:", decodedKey);
      }

      await db(plainTableName)
        .where("id", record_id)
        .update({ [fieldName]: null });

      return sendResponse(res, 200, "Record deleted successfully", true);
    } catch (error: any) {
      console.error("Delete error:", error);
      return sendResponse(
        res,
        500,
        error.message || "Failed to delete record",
        false
      );
    }
  }
);
