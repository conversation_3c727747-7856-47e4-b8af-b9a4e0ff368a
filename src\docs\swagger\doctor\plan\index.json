{"paths": {"/doctor/plans": {"get": {"summary": "Get All Plans (Doctor)", "tags": ["Doctor Plan"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "example": 1}, "description": "Page number for pagination"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "example": 10}, "description": "Number of items per page"}], "responses": {"200": {"description": "Plans fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"plans": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Basic Aligner"}, "type": {"type": "string", "example": "aligner"}, "duration_years": {"type": "integer", "example": 2}, "expiration_date": {"type": "string", "format": "date", "example": "2025-12-31"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/doctor/plan/{id}": {"get": {"summary": "Get Plan by ID (Doctor)", "tags": ["Doctor Plan"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "example": 1}, "description": "Plan ID"}], "responses": {"200": {"description": "Plan fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Basic Aligner"}, "type": {"type": "string", "example": "aligner"}, "duration_years": {"type": "integer", "example": 2}, "expiration_date": {"type": "string", "format": "date", "example": "2025-12-31"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}}}, "404": {"description": "Plan not found"}, "500": {"description": "Internal server error"}}}}}}