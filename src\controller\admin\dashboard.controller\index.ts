import { Request, Response } from "express";
import db from "../../../config/db";
import { TABLE } from "../../../utils/Database/table";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";
import { UserRole } from "../../../utils/enums/users.enum";


export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    // Count doctors
    const [{ count: doctorCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .where(`${TABLE.USERS}.is_deleted`, false)
      .count({ count: "*" });

    // Count patients
    const [{ count: patientCount }] = await db(TABLE.PATIENTS).count({ count: "*" });

    // Count employees
    const [{ count: specialistCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "specialist")
      .count({ count: "*" });

    // Count pending patients (status = 'sent_by_doctor')
    const pendingPatientsSubquery = db(TABLE.PATIENTS_VERSIONS)
      .select('patient_id')
      .whereIn('id', function () {
        this.select(db.raw('MAX(id)'))
          .from(TABLE.PATIENTS_VERSIONS)
          .groupBy('patient_id');
      })
      .where('status', 'sent_by_doctor');

    const [{ count: pendingPatientCount }] = await db(TABLE.PATIENTS)
      .whereIn('id', pendingPatientsSubquery)
      .count({ count: "*" });

    // Count incomplete patients (patients with no patient versions)
    const [{ count: incompletePatientCount }] = await db(TABLE.PATIENTS)
      .leftJoin(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, "=", `${TABLE.PATIENTS_VERSIONS}.patient_id`)
      .whereNull(`${TABLE.PATIENTS_VERSIONS}.patient_id`)
      .count({ count: `${TABLE.PATIENTS}.id` });

    const completedPatientsSubquery = db(TABLE.PATIENTS_VERSIONS)
      .select('patient_id')
      .whereIn('id', function () {
        this.select(db.raw('MAX(id)'))
          .from(TABLE.PATIENTS_VERSIONS)
          .groupBy('patient_id');
      })
      .where('status', 'completed');
    // Count complete patients (patients with at least one patient version)

    const [{ count: completePatientCount }] = await db(TABLE.PATIENTS)
      .whereIn('id', completedPatientsSubquery)
      .count({ count: "*" });

    sendResponse(res, 200, "Dashboard stats fetched", true, {
      doctors: Number(doctorCount),
      patients: Number(patientCount),
      specialist: Number(specialistCount),
      pendingPatients: Number(pendingPatientCount),
      incompletePatients: Number(incompletePatientCount),
      completePatients: Number(completePatientCount),
    });
  } catch (error: any) {
    sendResponse(res, 500, error.message, false);
  }
};


export const getDoctorsWithPatientCount = async (req: Request, res: Response) => {
  try {
    const { filter } = req.query;
    const filterType = filter || "active";
    const { order } = req.query;
    const sortOrder = order === "asc" ? "asc" : "desc";
    const pageLimit = parseInt(req.query.limit as string) || 10;
    const pageNumber = parseInt(req.query.page as string) || 1;
    const skip = (pageNumber - 1) * pageLimit;

    let countCondition = `COUNT(${TABLE.PATIENTS}.id)`;
    if (filterType === "active") {
      countCondition = `COUNT(CASE WHEN ${TABLE.PATIENTS}.is_deleted = false THEN 1 END)`;
    } else {
      countCondition = `COUNT(CASE WHEN ${TABLE.PATIENTS}.is_deleted = true THEN 1 END)`;
    }


    let query = db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .leftJoin(TABLE.PATIENTS, `${TABLE.PATIENTS}.doctor_id`, "=", `${TABLE.USERS}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .groupBy(`${TABLE.USERS}.id`, `${TABLE.USERS}.first_name`, `${TABLE.USERS}.last_name`)
      .select(
        `${TABLE.USERS}.id as doctor_id`,
        db.raw(`CONCAT(${TABLE.USERS}.first_name, ' ', ${TABLE.USERS}.last_name) as doctor_name`),
        db.raw(`${countCondition} as patient_count`)
      );

    const totalCountQuery = db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .count('* as total');

    const totalCountResult = await totalCountQuery.first();
    const totalCount = totalCountResult ? parseInt(totalCountResult.total as string) : 0;
    const totalPages = Math.ceil(totalCount / pageLimit);

    query = query
      .orderBy('patient_count', sortOrder)
      .offset(skip)
      .limit(pageLimit);

    const doctorsWithCounts = await query;

    const responseData = {
      doctors: doctorsWithCounts,
      filter: filterType,
      order: sortOrder,
      page: pageNumber,
      limit: pageLimit,
      totalCount,
      totalPages,
    };

    sendResponse(res, 200, "Doctors with patient count fetched", true, responseData);
  } catch (error: any) {
    sendResponse(res, 500, error.message, false);
  }
};

// Get monthly counts of doctors, specialists and patients
export const getMonthlyCounts = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const yearParam = req.query.year;
    const defaultYear = new Date().getFullYear();
    let currentYear: number;
    if (typeof yearParam === "string") {
      currentYear = parseInt(yearParam, 10);
    } else if (typeof yearParam === "number") {
      currentYear = yearParam;
    } else {
      currentYear = defaultYear;
    }
    if (isNaN(currentYear) || currentYear < 2000 || currentYear > 2100) {
      res.status(400).json({
        status: false,
        message: "Invalid year parameter",
      });
      return;
    }

    const roles = await db(TABLE.ROLES)
      .select("id", "role_name")
      .whereIn("role_name", [UserRole.DOCTOR, UserRole.SPECIALIST]);

    const roleMap: { [key: string]: number } = {};
    roles.forEach((role: any) => {
      roleMap[role.role_name] = role.id;
    });

    const doctorRoleId = roleMap[UserRole.DOCTOR];
    const specialistRoleId = roleMap[UserRole.SPECIALIST];

    if (!doctorRoleId || !specialistRoleId) {
      res.status(500).json({
        status: false,
        message: "Role configuration not found in database",
      });
      return;
    }

    // Get monthly counts for doctors
    const doctorsCount = await db(TABLE.USERS)
      .select(
        db.raw("EXTRACT(MONTH FROM created_at) as month"),
        db.raw("COUNT(*) as count")
      )
      .where("role_id", doctorRoleId)
      .where(db.raw("EXTRACT(YEAR FROM created_at) = ?", [currentYear]))
      .groupBy("month")
      .orderBy("month");

    // Get monthly counts for specialists
    const specialistsCount = await db(TABLE.USERS)
      .select(
        db.raw("EXTRACT(MONTH FROM created_at) as month"),
        db.raw("COUNT(*) as count")
      )
      .where("role_id", specialistRoleId)
      .where(db.raw("EXTRACT(YEAR FROM created_at) = ?", [currentYear]))
      .groupBy("month")
      .orderBy("month");

    // Get monthly counts for patients
    // const patientsCount = await db(TABLE.PATIENTS)
    //   .select(
    //     db.raw("EXTRACT(MONTH FROM created_at) as month"),
    //     db.raw("COUNT(*) as count")
    //   )
    //   .where(db.raw("EXTRACT(YEAR FROM created_at) = ?", [currentYear]))
    //   .groupBy("month")
    //   .orderBy("month");
    const patientsCount = await db(TABLE.PATIENTS_VERSIONS)
      .select(
        db.raw("EXTRACT(MONTH FROM created_at) as month"),
        db.raw("COUNT(DISTINCT patient_id) as count")
      )
      .where(db.raw("EXTRACT(YEAR FROM created_at) = ?", [currentYear]))
      .groupBy("month")
      .orderBy("month");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const monthlyData = monthNames.map((name) => ({
      month: name,
      doctors: 0,
      specialists: 0,
      patients: 0,
    }));

    doctorsCount.forEach((item: any) => {
      const monthIndex = Number(item.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        monthlyData[monthIndex].doctors = parseInt(item.count);
      }
    });

    specialistsCount.forEach((item: any) => {
      const monthIndex = Number(item.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        monthlyData[monthIndex].specialists = parseInt(item.count);
      }
    });

    patientsCount.forEach((item: any) => {
      const monthIndex = Number(item.month) - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        monthlyData[monthIndex].patients = parseInt(item.count);
      }
    });

    res.json({
      status: "200",
      success: true,
      data: monthlyData,
      message: "Monthly statistics fetched successfully",
    });
  } catch (error: any) {
    console.error("Error fetching monthly counts:", error);
    res.status(500).json({
      status: false,
      message: "Failed to fetch monthly statistics",
      error: error.message,
    });
  }
};
