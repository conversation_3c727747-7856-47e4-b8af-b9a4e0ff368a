import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('users', (table) => {
    table.integer('failed_login_attempts').defaultTo(0);
    table.timestamp('last_failed_login_at').nullable();
    table.boolean('is_locked').defaultTo(false);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('users', (table) => {
    table.dropColumn('failed_login_attempts');
    table.dropColumn('last_failed_login_at');
    table.dropColumn('is_locked');
  });
}
