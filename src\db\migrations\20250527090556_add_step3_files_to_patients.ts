import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('patients', (table) => {
    table.string('stlFile1').nullable();
    table.string('stlFile2').nullable();
    table.string('cbctFile').nullable();
    table.string('profileRepose').nullable();
    table.string('buccalRight').nullable();
    table.string('buccalLeft').nullable();
    table.string('frontalRepose').nullable();
    table.string('frontalSmiling').nullable();
    table.string('labialAnterior').nullable();
    table.string('occlussalLower').nullable();
    table.string('occlussalUpper').nullable();
    table.string('radioGraph1').nullable();
    table.string('radioGraph2').nullable();
    // Step 4 JSON column
    table.json('data').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('patients', (table) => {
    table.dropColumn('stlFile1');
    table.dropColumn('stlFile2');
    table.dropColumn('cbctFile');
    table.dropColumn('profileRepose');
    table.dropColumn('buccalRight');
    table.dropColumn('buccalLeft');
    table.dropColumn('frontalRepose');
    table.dropColumn('frontalSmiling');
    table.dropColumn('labialAnterior');
    table.dropColumn('occlussalLower');
    table.dropColumn('occlussalUpper');
    table.dropColumn('radioGraph1');
    table.dropColumn('radioGraph2');
    table.dropColumn('data');
  });
}
