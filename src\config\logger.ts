import winston from 'winston';
import path from 'path';
import fs from 'fs';
// Clean console format for better UI
const cleanConsoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    // Clean and format meta data for better display
    const cleanMeta = { ...meta };
    
    // Remove timestamp from meta to avoid duplication
    delete cleanMeta.timestamp;
    
    // Truncate long strings for display
    if (cleanMeta.userAgent && typeof cleanMeta.userAgent === 'string' && cleanMeta.userAgent.length > 60) {
      cleanMeta.userAgent = cleanMeta.userAgent.substring(0, 57) + '...';
    }
    
    // Format stack traces better
    if (cleanMeta.stack && typeof cleanMeta.stack === 'string') {
      cleanMeta.stack = cleanMeta.stack.split('\n').slice(0, 3).join('\n      ');
    }
    
    // Format error objects
    if (cleanMeta.error && typeof cleanMeta.error === 'string' && cleanMeta.error.length > 100) {
      cleanMeta.error = cleanMeta.error.substring(0, 97) + '...';
    }
    
    // Create clean formatted output
    let output = `🕐 ${timestamp} ${level}: ${message}`;
    
    // Add important metadata in a single line for access logs
    if (cleanMeta.method && cleanMeta.url && cleanMeta.status) {
      output += ` | ${cleanMeta.method} ${cleanMeta.url} → ${cleanMeta.status} (${cleanMeta.responseTime}ms)`;
      return output;
    }
    
    // Add other metadata in structured format for errors/other logs
    if (Object.keys(cleanMeta).length > 0) {
      const metaEntries = Object.entries(cleanMeta)
        .filter(([key]) => key !== 'timestamp')
        .map(([key, value]) => {
          if (typeof value === 'object') {
            return `${key}: ${JSON.stringify(value)}`;
          }
          return `${key}: ${value}`;
        });
      
      if (metaEntries.length > 0) {
        output += ` | ${metaEntries.join(', ')}`;
      }
    }
    
    return output;
  })
);

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for structured logging
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta
    });
  })
);

// Create separate loggers for different types
export const errorLogger = winston.createLogger({
  level: 'error',
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    new winston.transports.Console({
      format: cleanConsoleFormat
    })
  ]
});

export const accessLogger = winston.createLogger({
  level: 'info',
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'access.log'),
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 10,
      tailable: true
    }),
    new winston.transports.Console({
      format: cleanConsoleFormat
    })
  ]
});

export const appLogger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'app.log'),
      maxsize: 15 * 1024 * 1024, // 15MB
      maxFiles: 7,
      tailable: true
    }),
    new winston.transports.Console({
      format: cleanConsoleFormat
    })
  ]
});

// Database logger for SQL queries and operations
export const dbLogger = winston.createLogger({
  level: 'info',
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'database.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    new winston.transports.Console({
      format: cleanConsoleFormat
    })
  ]
});

export default appLogger;