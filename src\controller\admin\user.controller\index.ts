import { Request, Response } from "express";
import asyncHandler from "../../../middlewares/trycatch";
import bcrypt from "bcryptjs";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";
import { UserRole } from "../../../utils/enums/users.enum";
import validate from "../../../validations";
import {
  updateUserSchema,
} from "../../../validations/user.validation";
import { TABLE } from "../../../utils/Database/table";
import db from "../../../config/db";
import { sendPasswordResetEmail } from "../../../utils/services/nodemailer/doctorCredential";
import { Knex } from 'knex';

const BASE_URL = process.env.BASE_URL || "http://localhost:3000/uploads";

const fileFields = [
  "stlFile1",
  "stlFile2",
  "cbctFile",
  "profileRepose",
  "buccalRight",
  "buccalLeft",
  "frontalRepose",
  "frontalSmiling",
  "labialAnterior",
  "occlussalLower",
  "occlussalUpper",
  "radioGraph1",
  "radioGraph2"
];

function addBaseUrlToFiles(patient: any) {
  if (!patient) return patient;
  fileFields.forEach((field) => {
    if (patient[field]) {
      patient[field] = `${BASE_URL}/patients/${patient[field]}`;
    }
  });
  return patient;
}


// Login User
export const createUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { first_name, last_name, email, password, role_id, specialist_id } = req.body;

      // Email check
      if (await db(TABLE.USERS).where({ email }).first()) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // Generate 7‑digit unique ID
      let user_uuid: string;
      do {
        user_uuid = Math.floor(1000000 + Math.random() * 9000000).toString();
      } while (await db(TABLE.USERS).where({ user_uuid }).first());

      console.log("⏺️ About to insert user_uuid:", user_uuid);

      // Hash pwd and insert
      const hashedPassword = await bcrypt.hash(password, 10);
      const [newUser] = await db(TABLE.USERS)
        .insert({
          user_uuid,
          first_name,
          last_name,
          email,
          password: hashedPassword,
          role: role_id,
          specialist_id: role_id === UserRole.DOCTOR ? specialist_id : null,
        })
        .returning([
          "id", "user_uuid", "first_name", "last_name", "email", "role", "specialist_id"
        ]);

      // Step 6: Extra DB fetch to confirm saved correctly
      const dbCheck = await db(TABLE.USERS).where({ id: newUser.id }).first();
      console.log("🎯 Row from DB:", dbCheck);

      sendResponse(res, 201, "User created successfully", true, newUser);
    } catch (error: any) {
      console.error("❌ Error creating user:", error);
      sendResponse(res, 500, error.message, false);
    }
  }
);



export const updateUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const validationResult = validate(updateUserSchema, req.body, res);

      if (!validationResult.success) {
        sendResponse(res, 400, "Validation error", false);
        return;
      }

      const { first_name, last_name, email, username } = validationResult.data;

      // Check if the email is unique (exclude the current user by their ID and only check non-deleted users)
      if (email) {
        const existingUser = await db(TABLE.USERS)
          .where({ email: email })
          .where("is_deleted", false)
          .whereNot("id", id)
          .first();

        if (existingUser) {
          sendResponse(res, 400, "Email is already exists", false);
          return;
        }
      }

      const updatedUser = await db(TABLE.USERS)
        .where({ id, is_deleted: false }) // Find user by ID and ensure not soft-deleted
        .update({
          first_name,
          last_name,
          email,
          username
        })
        .returning([
          "id",
          "first_name",
          "last_name",
          "email",
          "is_active",
          "username",
          "is_verified",
        ]);

      if (!updatedUser) {
        sendResponse(res, 400, "User not found", false);
        return;
      }

      sendResponse(res, 200, "User updated successfully", true, updatedUser);
      return;
    } catch (error: any) {
      console.log(error);
      sendResponse(res, 500, error.message, false);
      return;
    }
  }
);

export const getAllUsers = asyncHandler(async (req: Request, res: Response) => {
  try {
    console.log("Update User Request Body:", req.body);
  const { page, limit, order, status } = req.query;
  const filterType = status || "active";
  const sortOrder = order === "asc" ? "asc" : "desc";
  const pageNumber = parseInt(page as string) || 1;
  const pageLimit = parseInt(limit as string) || 10;
  const skip = (pageNumber - 1) * pageLimit;


    // Fetch paginated users based on admin's user ID
    const users = await db(TABLE.USERS)
      .select(
        "id",
        "first_name",
        "last_name",
        "email",
        "role_id",
        "is_active",
        "is_verified"
      )
      .where("is_deleted", filterType === "deleted" ? true : false)
      .offset(skip)
      .limit(pageLimit)
      .orderBy("created_at", sortOrder);

    // Fetch the total count for pagination (excluding soft-deleted users)
    const totalCountResult = await db(TABLE.USERS)
      .where("is_deleted", filterType === "deleted" ? true : false)
      .count({ count: "*" });

    const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

    const data = {
      users,
      page: pageNumber,
      limit: pageLimit,
      totalCount,
      totalPages: Math.ceil(totalCount / pageLimit),
    };

    sendResponse(res, 200, "User fetched successfully", true, data);
    return;
  } catch (error: any) {
    console.error(error);
    sendResponse(res, 500, error.message, false);
    return;
  }
});

export const getUsersById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Fetch user by ID (only non-deleted users)
      const user = await db(TABLE.USERS)
        .select(
          "id",
          "user_uuid",
          "first_name",
          "last_name",
          "email",
          "role_id",
          "username",
          "is_active",
          "is_verified"
        )
        .where("id", id)
        .where("is_deleted", false)
        .first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      sendResponse(res, 200, "User fetched successfully", true, user);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const destroyUserById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Find the user to delete (only non-deleted users)
      const user = await db(TABLE.USERS)
        .where("id", id)
        .where("is_deleted", false)
        .first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      // Use transaction to ensure data consistency
      await db.transaction(async (trx) => {
        // Soft delete the user - keeping employee relationships intact
        await trx(TABLE.USERS)
          .where("id", id)
          .update({ 
            is_deleted: true,
            updated_at: new Date()
          });
      });

      sendResponse(res, 200, "User deleted successfully", true);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const deActiveUserById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const {status} = req.body;
      if(!status){
        return sendResponse(res, 400, "Status is required", false);
      }

      // Find the user to delete (only non-deleted users)
      const user = await db(TABLE.USERS)
        .where("id", id)
        .where("is_deleted", false)
        .first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      await db.transaction(async (trx) => {
        await trx(TABLE.USERS)
          .where("id", id)
          .update({ 
            is_active: status=="activate" ? true : false,
            updated_at: new Date()
          });
      });

      sendResponse(res, 200, `The user account has been ${status=="activate"? "ativated": "deactivated"} successfully.`, true);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const getDeletedUsers = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { page, limit } = req.query;

      const pageSize = parseInt(page as string) || 1;
      const pageLimit = parseInt(limit as string) || 10;
      const skip = (pageSize - 1) * pageLimit;

      // Fetch paginated deleted users
      const deletedUsers = await db(TABLE.USERS)
        .select(
          "id",
          "user_uuid",
          "first_name",
          "last_name",
          "email",
          "role_id",
          "is_active",
          "is_verified",
          "created_at",
          "updated_at"
        )
        .where("is_deleted", true)
        .offset(skip)
        .limit(pageLimit)
        .orderBy("updated_at", "desc"); // Show recently deleted first

      // Fetch the total count for pagination
      const totalCountResult = await db(TABLE.USERS)
        .where("is_deleted", true)
        .count({ count: "*" });

      const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

      const data = {
        users: deletedUsers,
        page: pageSize,
        limit: pageLimit,
        totalCount,
        totalPages: Math.ceil(totalCount / pageLimit),
      };

      sendResponse(res, 200, "Deleted users fetched successfully", true, data);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const restoreUserById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Find the deleted user
      const user = await db(TABLE.USERS)
        .where("id", id)
        .where("is_deleted", true)
        .first();

      if (!user) {
        return sendResponse(res, 400, "Deleted user not found", false);
      }

      // Restore the user
      const [restoredUser] = await db(TABLE.USERS)
        .where("id", id)
        .update({ 
          is_deleted: false,
          updated_at: new Date()
        })
        .returning([
          "id", 
          "user_uuid", 
          "first_name", 
          "last_name", 
          "email", 
          "role_id", 
          "is_active",
          "is_verified"
        ]);

      sendResponse(res, 200, "User restored successfully", true, restoredUser);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const getAllDoctors = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
  const { filter } = req.query;
  const filterType = filter || "active";
      const { order } = req.query;
      const sortOrder = order === "asc" ? "asc" : "desc";
      const pageNumber = parseInt(req.query.page as string) || 1;
      const pageLimit = parseInt(req.query.limit as string) || 10;
      const skip = (pageNumber - 1) * pageLimit;

      const doctorRole = await db(TABLE.ROLES)
        .where("role_name", "doctor")
        .first();

      if (!doctorRole) {
        sendResponse(res, 500, "Role 'doctor' not found in roles table", false);
        return;
      }

      let query = db(TABLE.USERS)
        .leftJoin(TABLE.ROLES, "users.role_id", "roles.id")
        .select(
          "users.id",
          "users.user_uuid",
          "users.first_name",
          "users.last_name",
          "users.email",
          "users.specialist_id",
          "users.is_active",
          "users.username",
          "users.is_verified",
          "users.is_deleted",
          "users.created_at",
          "roles.role_name as role"
        )
        .where("users.role_id", doctorRole.id);


      if (filterType === "deleted") {
        query = query.where("users.is_deleted", true);
      } else if (filterType === "inactive") {
        query = query.where("users.is_deleted", false).where("users.is_active", false);
      } else if (filterType === "active") {
        query = query.where("users.is_deleted", false).where("users.is_active", true);
      } else {
        // fallback: show all non-deleted
        query = query.where("users.is_deleted", false);
      }


      // Get total count for pagination
      let countQuery = db(TABLE.USERS)
        .where("role_id", doctorRole.id);
      if (filterType === "deleted") {
        countQuery = countQuery.where("is_deleted", true);
      } else if (filterType === "inactive") {
        countQuery = countQuery.where("is_deleted", false).where("is_active", false);
      } else if (filterType === "active") {
        countQuery = countQuery.where("is_deleted", false).where("is_active", true);
      } else {
        countQuery = countQuery.where("is_deleted", false);
      }
      const totalCountResult = await countQuery.count({ count: "*" });
      const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;
      const totalPages = Math.ceil(totalCount / pageLimit);

      query = query.orderBy("users.created_at", sortOrder)
        .offset(skip)
        .limit(pageLimit);

      const doctors = await query;

      let message = "Active doctors fetched successfully";
      if (filterType === "deleted") {
        message = "Deleted doctors fetched successfully";
      } else if (filterType === "inactive") {
        message = "Inactive doctors fetched successfully";
      }

      const responseData = {
        doctors,
        filter: filterType,
        page: pageNumber,
        limit: pageLimit,
        totalCount,
        totalPages
      };

      sendResponse(res, 200, message, true, responseData);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllSpecialists = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { filter, order } = req.query;
      const filterType = filter || "active";
      const sortOrder = order === "asc" ? "asc" : "desc";
      const pageNumber = parseInt(req.query.page as string) || 1;
      const pageLimit = parseInt(req.query.limit as string) || 10;
      const skip = (pageNumber - 1) * pageLimit;

      const specialistRole = await db(TABLE.ROLES)
        .where("role_name", "specialist")
        .first();

      if (!specialistRole) {
        sendResponse(res, 500, "Role 'specialist' not found in roles table", false);
        return;
      }

      let query = db(TABLE.USERS)
        .leftJoin(TABLE.ROLES, "users.role_id", "roles.id")
        .select(
          "users.id",
          "users.user_uuid",
          "users.first_name",
          "users.last_name",
          "users.email",
          "users.specialist_id",
          "users.is_active",
          "users.username",
          "users.is_verified",
          "users.is_deleted",
          "users.created_at",
          "roles.role_name as role"
        )
        .where("users.role_id", specialistRole.id);

      if (filterType === "deleted") {
        query = query.where("users.is_deleted", true);
      } else if (filterType === "inactive") {
        query = query.where("users.is_deleted", false).where("users.is_active", false);
      } else if (filterType === "active") {
        query = query.where("users.is_deleted", false).where("users.is_active", true);
      } else {
        // fallback: show all non-deleted
        query = query.where("users.is_deleted", false);
      }

      // Get total count for pagination
      let countQuery = db(TABLE.USERS)
        .where("role_id", specialistRole.id);
      if (filterType === "deleted") {
        countQuery = countQuery.where("is_deleted", true);
      } else if (filterType === "inactive") {
        countQuery = countQuery.where("is_deleted", false).where("is_active", false);
      } else if (filterType === "active") {
        countQuery = countQuery.where("is_deleted", false).where("is_active", true);
      } else {
        countQuery = countQuery.where("is_deleted", false);
      }
      const totalCountResult = await countQuery.count({ count: "*" });
      const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;
      const totalPages = Math.ceil(totalCount / pageLimit);

      query = query.orderBy("users.created_at", sortOrder)
        .offset(skip)
        .limit(pageLimit);

      const specialists = await query;

      let message = "Active specialists fetched successfully";
      if (filterType === "deleted") {
        message = "Deleted specialists fetched successfully";
      } else if (filterType === "inactive") {
        message = "Inactive specialists fetched successfully";
      }

      const responseData = {
        specialists,
        filter: filterType,
        page: pageNumber,
        limit: pageLimit,
        totalCount,
        totalPages
      };
      sendResponse(res, 200, message, true, responseData);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getPatientById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      let patient = await db(TABLE.PATIENTS)
        .where({ id })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // Add file URLs
      patient = addBaseUrlToFiles(patient);

      // Fetch plan info
      let plan = null;
      if (patient.plan_id) {
        plan = await db(TABLE.PLANS).where({ id: patient.plan_id }).first();
      }

      // Fetch ship_to_office and bill_to_office addresses
      let shipToOffice = null;
      if (patient.ship_to_office) {
        shipToOffice = await db("doctor_addresses")
          .where({ id: patient.ship_to_office })
          .first();
      }
      let billToOffice = null;
      if (patient.bill_to_office) {
        billToOffice = await db("doctor_addresses")
          .where({ id: patient.bill_to_office })
          .first();
      }

      sendResponse(res, 200, "Patient fetched successfully", true, {
        ...patient,
        plan,
        ship_to_office: shipToOffice,
        bill_to_office: billToOffice,
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllPatientsWithDoctors = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { filter } = req.params;
      const filterType = filter || "active";
      const { order, status, incomplete, doctorId, search } = req.query;
      const sortOrder = order === "asc" ? "asc" : "desc";
      const pageNumber = parseInt(req.query.page as string) || 1;
      const pageLimit = parseInt(req.query.limit as string) || 10;
      const skip = (pageNumber - 1) * pageLimit;

      const userRole = (req as any).user?.role || (req as any).user?.role_name;
      const isAdmin = userRole === UserRole.ADMIN || userRole === UserRole.SUPERADMIN;

      const dat = await db(TABLE.PATIENTS).select("*");
      console.log(dat);

      const applySearch = (queryBuilder: Knex.QueryBuilder) => {
        if (search && typeof search === "string" && search.trim() !== "") {
          const s = `%${search.trim().toLowerCase().replace(/[-]/g, ' ')}%`;

          // Handle date normalization for search
          const normalizedDate = search.trim().replace(/(\d{4})[-/](\d{2})[-/](\d{2})/, '$1-$2-$3')
                                     .replace(/(\d{2})[-/](\d{2})[-/](\d{4})/, '$3-$2-$1');
console.log(search, normalizedDate);
          queryBuilder.andWhere(function (this: Knex.QueryBuilder) {
            this.whereRaw(`LOWER(${TABLE.PATIENTS}.first_name) LIKE ?`, [s])
              .orWhereRaw(`LOWER(${TABLE.PATIENTS}.last_name) LIKE ?`, [s])
              .orWhereRaw(`LOWER(${TABLE.PATIENTS}.dob::text) LIKE ?`, [`%${normalizedDate}%`])
              .orWhereRaw(`LOWER(REPLACE(${TABLE.PATIENTS}.country, '-', ' ')) LIKE ?`, [s])
              .orWhereRaw(`LOWER(pv.status) LIKE ?`, [s]);
          });
        }
      };

      if (incomplete === 'true' && isAdmin) {
        let query = db(TABLE.PATIENTS)
          .join(TABLE.USERS, `${TABLE.PATIENTS}.doctor_id`, '=', `${TABLE.USERS}.id`)
          .leftJoin(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, '=', `${TABLE.PATIENTS_VERSIONS}.patient_id`)
          .whereNull(`${TABLE.PATIENTS_VERSIONS}.id`)
          .select(
            `${TABLE.PATIENTS}.*`,
            `${TABLE.USERS}.first_name as doctor_first_name`,
            `${TABLE.USERS}.last_name as doctor_last_name`
          );

        if (filterType === "deleted") {
          query = query.where(`${TABLE.PATIENTS}.is_deleted`, true);
        } else {
          query = query.where(`${TABLE.PATIENTS}.is_deleted`, false);
        }
        if (doctorId) {
          query = query.where(`${TABLE.PATIENTS}.doctor_id`, doctorId);
        }

        applySearch(query);

        let countQuery = db(TABLE.PATIENTS)
          .leftJoin(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, '=', `${TABLE.PATIENTS_VERSIONS}.patient_id`)
          .whereNull(`${TABLE.PATIENTS_VERSIONS}.id`)
          .where(`${TABLE.PATIENTS}.is_deleted`, filterType === "deleted");
        if (doctorId) {
          countQuery = countQuery.where(`${TABLE.PATIENTS}.doctor_id`, doctorId);
        }

        applySearch(countQuery);

        const countResult = await countQuery.countDistinct(`${TABLE.PATIENTS}.id as count`).first();
        const totalCount = countResult ? parseInt(countResult.count as string) : 0;
        const totalPages = Math.ceil(totalCount / pageLimit);

        query = query.orderBy(`${TABLE.PATIENTS}.created_at`, sortOrder)
          .offset(skip)
          .limit(pageLimit);

        let patients = await query;
        patients = await Promise.all(
          patients.map(async (patient: any) => {
            const patientWithFiles = addBaseUrlToFiles(patient);
            return {
              ...patientWithFiles,
              versions: [],
            };
          })
        );

        const message = filterType === "deleted"
          ? "Deleted incomplete patients fetched successfully"
          : "Incomplete patients fetched successfully";

        const responseData = {
          patients,
          filter: filterType,
          incomplete: true,
          page: pageNumber,
          limit: pageLimit,
          totalCount,
          totalPages,
          search,
        };

        sendResponse(res, 200, message, true, responseData);
        return;
      }

      const latestVersionSubquery = `(
        SELECT patient_id, MAX(version_number) as max_version
        FROM ${TABLE.PATIENTS_VERSIONS}
        GROUP BY patient_id
      ) as latest_version`;

      let query = db(TABLE.PATIENTS)
        .join(TABLE.USERS, `${TABLE.PATIENTS}.doctor_id`, '=', `${TABLE.USERS}.id`)
        .joinRaw(
          `JOIN ${latestVersionSubquery} ON ${TABLE.PATIENTS}.id = latest_version.patient_id`
        )
        .joinRaw(
          `JOIN ${TABLE.PATIENTS_VERSIONS} pv ON pv.patient_id = ${TABLE.PATIENTS}.id AND pv.version_number = latest_version.max_version`
        )
        .select(
          `${TABLE.PATIENTS}.*`,
          `${TABLE.USERS}.first_name as doctor_first_name`,
          `${TABLE.USERS}.last_name as doctor_last_name`,
          db.raw('pv.status as latest_status')
        );

      if (filterType === "deleted") {
        query = query.where(`${TABLE.PATIENTS}.is_deleted`, true);
      } else {
        query = query.where(`${TABLE.PATIENTS}.is_deleted`, false);
      }
      if (doctorId) {
        query = query.where(`${TABLE.PATIENTS}.doctor_id`, doctorId);
      }

      if (status && status !== 'all') {
        query = query.where('pv.status', status);
      }

      applySearch(query);

      let countQuery = db(TABLE.PATIENTS)
        .joinRaw(
          `JOIN ${latestVersionSubquery} ON ${TABLE.PATIENTS}.id = latest_version.patient_id`
        )
        .joinRaw(
          `JOIN ${TABLE.PATIENTS_VERSIONS} pv ON pv.patient_id = ${TABLE.PATIENTS}.id AND pv.version_number = latest_version.max_version`
        )
        .where(`${TABLE.PATIENTS}.is_deleted`, filterType === "deleted");
      if (doctorId) {
        countQuery = countQuery.where(`${TABLE.PATIENTS}.doctor_id`, doctorId);
      }
      if (status && status !== 'all') {
        countQuery = countQuery.where('pv.status', status);
      }

      applySearch(countQuery);

      const countResult = await countQuery.countDistinct(`${TABLE.PATIENTS}.id as count`).first();
      const totalCount = countResult ? parseInt(countResult.count as string) : 0;
      const totalPages = Math.ceil(totalCount / pageLimit);

      query = query.orderBy(`${TABLE.PATIENTS}.created_at`, sortOrder)
        .offset(skip)
        .limit(pageLimit);

      let patients = await query;

      patients = await Promise.all(
        patients.map(async (patient: any) => {
          const patientWithFiles = addBaseUrlToFiles(patient);
          const versions = await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patient.id })
            .orderBy('version_number', 'asc');

          // Adjust and format the DOB to YYYY-MM-DD considering timezone
          if (patientWithFiles.dob) {
            const dobDate = new Date(patientWithFiles.dob);
            const adjustedDate = new Date(dobDate.getTime() + dobDate.getTimezoneOffset() * 60000);
            patientWithFiles.dob = adjustedDate.toISOString().split('T')[0];
          }

          return {
            ...patientWithFiles,
            versions,
          };
        })
      );

      const message = filterType === "deleted"
        ? "Deleted patients fetched successfully"
        : "Active patients fetched successfully";

      const responseData = {
        patients,
        filter: filterType,
        page: pageNumber,
        limit: pageLimit,
        totalCount,
        totalPages,
        search,
      };

      sendResponse(res, 200, message, true, responseData);

    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const getAllRoles = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const roles = await db(TABLE.ROLES).select('*').orderBy('id', 'asc');
      sendResponse(res, 200, 'Roles fetched successfully', true, roles);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const cancelPatientVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params; // patient ID

      // Check if patient exists
      const patient = await db(TABLE.PATIENTS)
        .where({ id })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // Get the latest patient version for this patient
      // ...existing code...
      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: id })
        .orderBy('version_number', 'desc') // or 'created_at', 'desc'
        .first();

      if (!latestVersion) {
        return sendResponse(res, 404, "No patient version found", false);
      }

      // Check if the current status is 'sent_by_doctor'
      if (latestVersion.status !== "sent_by_doctor") {
        return sendResponse(
          res,
          400,
          `Cannot cancel version with status '${latestVersion.status}'. Only versions with status 'sent_by_doctor' can be cancelled.`,
          false
        );
      }

      // Update the status to 'cancelled_by_admin'
      const [updatedVersion] = await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: latestVersion.id })
        .update({
          status: "cancelled_by_admin"
        })
        .returning("*");

      sendResponse(
        res,
        200,
        "Patient version cancelled successfully",
        true,
        {
          patient_id: id,
          version_id: updatedVersion.id,
          version_number: updatedVersion.version_number,
          old_status: "sent_by_doctor",
          new_status: "cancelled_by_admin"
        }
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const adminForgotPassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email, username } = req.body;

      // Validate input
      if (!email && !username) {
        sendResponse(
          res,
          400,
          "Please provide either email or username",
          false
        );
        return;
      }

      const existUser = await db(`${TABLE.USERS} as u`)
      .leftJoin(`${TABLE.ROLES} as r`, "u.role_id", "r.id")
      .select(
        "u.id as id",
        "u.email",
        "u.is_active",
        "u.is_verified",
        "u.is_deleted",
        "u.role_id",
        "r.role_name"
      )
      .where({ "u.email": email, "u.is_deleted": false })
      .first();

      if (
      existUser.role_name != UserRole.SUPERADMIN &&
      existUser.role_name != UserRole.ADMIN
    ) {
      sendResponse(
        res,
        403,
        "You are not authorized to perform this action",
        false
      );
      return;
    }
       

      if (!existUser) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      // Check verification status
      if (!existUser.is_verified) {
        sendResponse(res, 403, "Account is not verified", false);
        return;
      }

      // Generate random password (same as createUser function)
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      // Create and hash new password
      const newPassword = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password in database
      await db(TABLE.USERS)
        .where("id", existUser.id)
        .update({ password: hashedPassword });

      // Send email with new password
      try {
        await sendPasswordResetEmail({
          email: existUser.email,
          name: `${existUser.first_name} ${existUser.last_name}`,
          password: newPassword,
        });

        sendResponse(
          res,
          200,
          "New password sent to your registered email",
          true
        );
      } catch (emailError) {
        console.error("Email sending failed:", emailError);
        sendResponse(
          res,
          200,
          "Password reset but failed to send email. Please contact support.",
          true
        );
      }
    } catch (err) {
      console.error(err);
      sendResponse(res, 500, "Internal server error", false, err);
    }
  }
);

