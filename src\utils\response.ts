import { Response } from "express";
import { errorLogger, appLogger } from "../config/logger";

export const response = (res: Response, status: number, message: string) => {
  let success = false;
  if (status == 200 || status == 201) {
    success = true;
  }
  
  // Log the response
  appLogger.info('API Response', {
    status,
    success,
    message,
    endpoint: res.req?.originalUrl,
    method: res.req?.method,
    userAgent: res.req?.get('User-Agent'),
    ip: res.req?.ip || res.req?.connection?.remoteAddress
  });
  
  return res.status(status).json({ status, success, message });
};

export const responseData = (
  res: Response,
  status: number,
  message: string,
  data: any
) => {
  let success = false;
  if (status == 200 || status == 201) {
    success = true;
  }
  
  // Log the response (without sensitive data)
  appLogger.info('API Response with Data', {
    status,
    success,
    message,
    dataType: typeof data,
    hasData: !!data,
    endpoint: res.req?.originalUrl,
    method: res.req?.method,
    userAgent: res.req?.get('User-Agent'),
    ip: res.req?.ip || res.req?.connection?.remoteAddress
  });
  
  return res.status(status).json({ status, success, message, data });
};

export const errorResponse = (res: Response, message: string) => {
  errorLogger.error('API Error Response', {
    status: 400,
    message,
    endpoint: res.req?.originalUrl,
    method: res.req?.method,
    userAgent: res.req?.get('User-Agent'),
    ip: res.req?.ip || res.req?.connection?.remoteAddress,
    userId: (res.req as any)?.user?.id
  });
  
  return res.status(400).json({ status: 400, success: false, message });
};

export const errorCatchResponse = (res: Response, error: any) => {
  const message = error?.message || 'Internal server error';
  
  errorLogger.error('Unhandled Error', {
    status: 500,
    message,
    stack: error?.stack,
    endpoint: res.req?.originalUrl,
    method: res.req?.method,
    userAgent: res.req?.get('User-Agent'),
    ip: res.req?.ip || res.req?.connection?.remoteAddress,
    userId: (res.req as any)?.user?.id,
    error: error
  });
  
  return res.status(500).json({ status: 500, success: false, message });
};
