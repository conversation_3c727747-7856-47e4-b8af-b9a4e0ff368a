<!-- <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Patient-Centric Chat with File Sharing</title>
    <style>
      body {
        font-family: Arial;
        display: flex;
        height: 100vh;
        margin: 0;
      }

      .sidebar {
        width: 300px;
        border-right: 1px solid #ccc;
        overflow-y: auto;
        padding: 10px;
      }

      .chat {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px;
      }

      .message-container {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .message {
        margin: 10px 0;
        padding: 10px;
        border-radius: 8px;
        max-width: 80%;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .sent {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: 20%;
      }

      .received {
        background-color: #f0f0f0;
        align-self: flex-start;
        margin-right: 20%;
      }

      .message time {
        font-size: 0.7em;
        color: #666;
        display: block;
        margin-top: 5px;
        text-align: right;
      }

      input,
      button {
        padding: 10px;
        margin: 5px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .conversation {
        padding: 10px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s;
      }

      .conversation:hover {
        background-color: #f5f5f5;
      }

      .unread-badge {
        background: #ff3b30;
        color: white;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 10px;
        position: absolute;
        right: 10px;
        top: 10px;
      }

      .file-message {
        margin-top: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
      }

      .file-link {
        display: block;
        margin-top: 8px;
        color: #007aff;
        text-decoration: none;
      }

      .file-link:hover {
        text-decoration: underline;
      }

      .file-icon {
        margin-right: 5px;
        font-size: 16px;
      }

      .image-preview {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
        border-radius: 5px;
        border: 1px solid #eee;
      }

      .upload-btn {
        background: #5ac8fa;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        margin: 0 5px;
        transition: background-color 0.2s;
      }

      .upload-btn:hover {
        background: #4ab0e0;
      }

      .send-btn {
        background: #34c759;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .send-btn:hover {
        background: #2daa4a;
      }

      .file-info {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
      }

      .input-group {
        display: flex;
        margin-top: 10px;
      }

      .message-input {
        flex: 1;
        padding: 10px;
      }

      .file-input {
        display: none;
      }

      .delete-btn {
        background: #ff3b30;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8em;
        margin-top: 5px;
      }

      .patient-header {
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
      }
    </style>
  </head>
  <body>
    <div class="sidebar">
      <h3>Conversations</h3>

      <div>
        <input type="number" id="currentUserId" placeholder="Your User ID" />
        <input type="number" id="patientId" placeholder="Patient ID" />
        <button onclick="connectUser()" class="send-btn">Connect</button>
      </div>

      <div style="margin-top: 15px">
        <input type="number" id="receiverId" placeholder="Receiver User ID" />
        <button onclick="startChat()" class="send-btn">Start Chat</button>
      </div>

      <h4 style="margin-top: 20px">Conversations</h4>
      <div id="conversations"></div>
    </div>


        .patient-header {
          margin-bottom: 10px;
          padding-bottom: 10px;
          border-bottom: 1px solid #eee;
        }
      </style>
    </head>
    <body>
      <div class="sidebar">
        <h3>Conversations</h3>

        <div>
          <input type="number" id="currentUserId" placeholder="Your User ID" />
          <input type="number" id="patientId" placeholder="Patient ID" />
          <button onclick="connectUser()" class="send-btn">Connect</button>
        </div>

        <div style="margin-top: 15px">
          <input type="number" id="receiverId" placeholder="Receiver User ID" />
          <button onclick="startChat()" class="send-btn">Start Chat</button>
        </div>
        
        <div style="margin-top: 15px">
          <button onclick="connectToServer()" class="upload-btn" id="reconnectBtn">Reconnect</button>
          <button onclick="window.location.reload()" class="delete-btn">Refresh Page</button>
        </div>

        <h4 style="margin-top: 20px">Conversations</h4>
        <div id="conversations"></div>
      </div>

      <div class="message-container" id="messages"></div>

      <div class="input-group">
        <input
          type="text"
          id="messageInput"
          class="message-input"
          placeholder="Type a message..."
        />
        <button
          class="upload-btn"
          onclick="document.getElementById('fileInput').click()"
        >
          📎 Attach
        </button>
        <button onclick="sendMessage()" class="send-btn">Send</button>
      </div>
      <input type="file" id="fileInput" class="file-input" />

      <div id="filePreview" style="margin-top: 10px"></div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
      const socket = io("http://localhost:5004");
      let currentUserId = null;
      let currentReceiverId = null;
      let currentPatientId = null;
      let conversations = [];
      let currentFile = null;

      // Define allowed file types with more comprehensive MIME types for ZIP files
      const ALLOWED_FILE_TYPES = {
        "application/zip": "ZIP file",
        "application/x-zip": "ZIP file",
        "application/x-zip-compressed": "ZIP file",
        "application/octet-stream": "Binary file", // Some browsers report ZIP as octet-stream
        "application/pdf": "PDF document",
        "image/jpeg": "JPEG image",
        "image/png": "PNG image",
        "image/gif": "GIF image",
        "image/webp": "WebP image",
        "image/svg+xml": "SVG image",
      };

      // Define max file size (50 MB in bytes)
      const MAX_FILE_SIZE = 50 * 1024 * 1024;

      const conversationsDiv = document.getElementById("conversations");
      const messagesDiv = document.getElementById("messages");
      const patientNameSpan = document.getElementById("patientName");
      const fileInput = document.getElementById("fileInput");
      const filePreviewDiv = document.getElementById("filePreview");
      const messageInput = document.getElementById("messageInput");

      // Initialize
      fileInput.addEventListener("change", handleFileSelect);

      function connectUser() {
        currentUserId = document.getElementById("currentUserId").value;
        currentPatientId = document.getElementById("patientId").value;

        if (!currentUserId || !currentPatientId) {
          return alert("Please enter your User ID and Patient ID");
        }

        socket.emit("addUser", currentUserId);
        socket.emit("getConversations", {
          userId: parseInt(currentUserId),
          patientId: parseInt(currentPatientId),
        });


      <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
      <script>
        // Prevent multiple instances and connection flooding
        if (window.socketInstance) {
          window.socketInstance.disconnect();
          window.socketInstance = null;
        }

        const socket = io("http://localhost:5000", {
          transports: ["websocket", "polling"],
          timeout: 20000,
          forceNew: false,
          reconnection: true,
          reconnectionDelay: 2000,
          reconnectionDelayMax: 10000,
          reconnectionAttempts: 3,
          randomizationFactor: 0.5
        });

        // Store socket instance globally to prevent duplicates
        window.socketInstance = socket;

        // Prevent multiple connections by tracking connection state
        let isConnecting = false;
        let isConnected = false;
        let connectionAttempts = 0;
        const maxConnectionAttempts = 3;

        // Connection event handlers
        socket.on('connect', () => {
          isConnecting = false;
          isConnected = true;
          connectionAttempts = 0;
          console.log('Connected to server:', socket.id);
          
          // Show connection status to user
          document.body.style.backgroundColor = '';
          if (document.getElementById('connectionStatus')) {
            document.getElementById('connectionStatus').textContent = 'Connected';
            document.getElementById('connectionStatus').style.color = 'green';
          }
          
          // Auto-join user room if userId is available
          if (currentUserId) {
            console.log('Auto-joining room for user:', currentUserId);
            socket.emit("join", currentUserId);
          }
        });

        socket.on('disconnect', (reason) => {
          isConnected = false;
          console.log('Disconnected from server:', reason);
          
          // Show disconnection status
          document.body.style.backgroundColor = '#ffebee';
          if (document.getElementById('connectionStatus')) {
            document.getElementById('connectionStatus').textContent = 'Disconnected: ' + reason;
            document.getElementById('connectionStatus').style.color = 'red';
          }
          
          // Only attempt to reconnect for certain reasons and if not too many attempts
          if (connectionAttempts < maxConnectionAttempts && 
              (reason === 'io server disconnect' || reason === 'transport close') && 
              !isConnecting) {
            isConnecting = true;
            connectionAttempts++;
            console.log(`Attempting to reconnect (attempt ${connectionAttempts}/${maxConnectionAttempts})`);
            
            setTimeout(() => {
              if (!isConnected && !socket.connected && connectionAttempts <= maxConnectionAttempts) {
                socket.connect();
              }
              isConnecting = false;
            }, 3000 * connectionAttempts); // Exponential backoff
          }
        });

        socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          isConnecting = false;
          connectionAttempts++;
          
          if (document.getElementById('connectionStatus')) {
            document.getElementById('connectionStatus').textContent = 'Connection Error: ' + error.message;
            document.getElementById('connectionStatus').style.color = 'red';
          }
        });

        socket.on('reconnect', (attemptNumber) => {
          console.log('Reconnected after', attemptNumber, 'attempts');
          connectionAttempts = 0;
        });

        socket.on('reconnect_error', (error) => {
          console.error('Reconnection error:', error);
        });

        socket.on('reconnect_failed', () => {
          console.error('Failed to reconnect to server');
          if (document.getElementById('connectionStatus')) {
            document.getElementById('connectionStatus').textContent = 'Reconnection Failed';
            document.getElementById('connectionStatus').style.color = 'red';
          }
        });

        // Manual connection function - simplified
        function connectToServer() {
          if (!isConnected && !isConnecting) {
            isConnecting = true;
            console.log('Manually reconnecting to server...');
            if (socket.connected) {
              socket.disconnect();
            }
            setTimeout(() => {
              socket.connect();
            }, 1000);
          } else {
            console.log('Already connected or connecting...');
          }
        }

        // Add connection status indicator
        const statusDiv = document.createElement('div');
        statusDiv.id = 'connectionStatus';
        statusDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; padding: 5px 10px; background: #f0f0f0; border: 1px solid #ccc; border-radius: 3px; z-index: 1000;';
        statusDiv.textContent = 'Connecting...';
        document.body.appendChild(statusDiv);

        // Page visibility handling to prevent unnecessary connections
        document.addEventListener('visibilitychange', () => {
          if (document.hidden) {
            console.log('Page hidden, pausing reconnection attempts');
          } else {
            console.log('Page visible again');
            if (!isConnected && !isConnecting && connectionAttempts < maxConnectionAttempts) {
              console.log('Attempting to reconnect after page became visible');
              setTimeout(() => connectToServer(), 1000);
            }
          }
        });

        // Prevent connection attempts on page unload
        window.addEventListener('beforeunload', () => {
          if (socket) {
            socket.disconnect();
          }
        });

        let currentUserId = null;
        let currentReceiverId = null;
        let currentPatientId = null;
        let conversations = [];
        let currentFile = null;

        // Define allowed file types with more comprehensive MIME types for ZIP files
        const ALLOWED_FILE_TYPES = {
          'application/zip': 'ZIP file',
          'application/x-zip': 'ZIP file',
          'application/x-zip-compressed': 'ZIP file', 
          'application/octet-stream': 'Binary file', // Some browsers report ZIP as octet-stream
          'application/pdf': 'PDF document',
          'image/jpeg': 'JPEG image',
          'image/png': 'PNG image',
          'image/gif': 'GIF image',
          'image/webp': 'WebP image',
          'image/svg+xml': 'SVG image'
        };

        // Define max file size (50 MB in bytes)
        const MAX_FILE_SIZE = 50 * 1024 * 1024;

        const conversationsDiv = document.getElementById("conversations");
        const messagesDiv = document.getElementById("messages");
        const patientNameSpan = document.getElementById("patientName");
        const fileInput = document.getElementById("fileInput");
        const filePreviewDiv = document.getElementById("filePreview");
        const messageInput = document.getElementById("messageInput");

        // Initialize
        fileInput.addEventListener("change", handleFileSelect);

        function connectUser() {
          currentUserId = document.getElementById("currentUserId").value;
          currentPatientId = document.getElementById("patientId").value;

          if (!currentUserId || !currentPatientId) {
            return alert("Please enter your User ID and Patient ID");
          }

          // If already connected, just join and get conversations
          if (isConnected) {
            console.log('Already connected, joining room and getting conversations...');
            socket.emit("join", currentUserId);
            socket.emit("getConversations", {
              userId: parseInt(currentUserId),
              patientId: parseInt(currentPatientId),
            });
          } else {
            // Not connected, wait for connection then proceed
            console.log('Not connected, waiting for connection...');
            socket.once('connect', () => {
              console.log('Connected! Now joining room and getting conversations...');
              socket.emit("join", currentUserId);
              socket.emit("getConversations", {
                userId: parseInt(currentUserId),
                patientId: parseInt(currentPatientId),
              });
            });

          }


        socket.emit("getConversationMessages", {
          user_id: parseInt(currentUserId),
          target_user_id: parseInt(currentReceiverId),
          patient_id: parseInt(currentPatientId),
        });

        socket.emit("markMessagesAsRead", {
          user_id: parseInt(currentUserId),
          sender_id: parseInt(currentReceiverId),
          patient_id: parseInt(currentPatientId),
        });

        updateUnreadCount(currentReceiverId, 0);
      }

      function renderConversations(list) {
        conversationsDiv.innerHTML = "";

        if (list.length === 0) {
          conversationsDiv.innerHTML = "<p>No conversations yet</p>";
          return;
        }

        list.forEach((conv) => {
          const div = document.createElement("div");
          div.className = "conversation";
          div.onclick = () => {
            currentReceiverId = conv.user.id;
            socket.emit("getConversationMessages", {
              user_id: parseInt(currentUserId),
              target_user_id: parseInt(currentReceiverId),
              patient_id: parseInt(currentPatientId),
            });

            socket.emit("markMessagesAsRead", {
              user_id: parseInt(currentUserId),
              sender_id: parseInt(currentReceiverId),
              patient_id: parseInt(currentPatientId),
            });

            updateUnreadCount(currentReceiverId, 0);
          };

          div.innerHTML = `
            <strong>${conv.user.name}</strong>
            ${
              conv.unread_count > 0
                ? `<span class="unread-badge">${conv.unread_count}</span>`
                : ""
            }
            <div style="display: flex; justify-content: space-between;">
              <small>${
                conv.message
                  ? conv.message.substring(0, 30) +
                    (conv.message.length > 30 ? "..." : "")
                  : ""
              }</small>
              <small>${new Date(conv.created_at).toLocaleTimeString()}</small>
            </div>
          `;
          conversationsDiv.appendChild(div);
        });
      }

      function renderMessages(messages) {
        messagesDiv.innerHTML = "";

        if (messages.length === 0) {
          messagesDiv.innerHTML =
            "<p>No messages yet. Start the conversation!</p>";
          return;
        }

        messages.forEach((msg) => {
          const div = document.createElement("div");
          div.className =
            msg.sender_id == currentUserId
              ? "message sent"
              : "message received";


            let content = msg.message || "";8


          if (msg.file_url) {
            content += `<div class="file-message">
              <strong>Attached file:</strong> ${msg.file_name}
              <div class="file-info">${formatFileSize(msg.file_size)} - ${

                msg.file_type
              }</div>
              <a href="http://localhost:5000${msg.file_url}" 

                class="file-link" 
                target="_blank"
                download="${msg.file_name}">
                📥 Download File
              </a>`;


              // Show appropriate icon or preview based on file type
              if (msg.file_type.startsWith("image/")) {
                content += `<img src="http://localhost:5000${msg.file_url}" 

                          alt="${msg.file_name}" 
                          class="image-preview">`;
            } else if (msg.file_type === "application/pdf") {
              content += `<div class="file-icon">📄 PDF Document</div>`;
            } else if (
              msg.file_type === "application/zip" ||
              msg.file_type === "application/x-zip-compressed" ||
              msg.file_name.toLowerCase().endsWith(".zip")
            ) {
              content += `<div class="file-icon">🗜️ ZIP Archive</div>`;
            }
            content += `</div>`;
          }

          div.innerHTML = `
            <div>${content}</div>
            <time>${new Date(msg.created_at).toLocaleString()}</time>
            ${
              msg.sender_id == currentUserId
                ? `<button class="delete-btn" onclick="deleteMessage(${msg.id})">Delete</button>`
                : ""
            }
          `;

            messagesDiv.appendChild(div);
          });
          messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Add missing isFileTypeAllowed function
        function isFileTypeAllowed(fileType) {
          // Direct match with allowed types
          if (Object.keys(ALLOWED_FILE_TYPES).includes(fileType)) {
            return true;
          }
          
          // For octet-stream, we need to check the extension
          if (fileType === 'application/octet-stream') {
            return true; // Allow octet-stream as we check extension separately
          }
          
          return false;
        }

        function handleFileSelect(event) {
          const file = event.target.files[0];
          if (!file) return;

          // Log file type for debugging
          console.log("Selected file type:", file.type);
          console.log("Selected file name:", file.name);
          console.log("Selected file size:", file.size);
          
          // Validate file size
          if (file.size > MAX_FILE_SIZE) {
            alert("File size exceeds the 50 MB limit. Please select a smaller file.");
            fileInput.value = ""; // Clear the file input
            return;
          }

          // Validate file type with special handling for ZIP files
          const fileExtension = file.name.split('.').pop().toLowerCase();
          let isAllowed = isFileTypeAllowed(file.type);
          
          // Special handling for ZIP files that might have unusual MIME types
          if (!isAllowed && fileExtension === 'zip') {
            console.log("Allowing ZIP file based on extension despite MIME type:", file.type);
            isAllowed = true;
          }
          
          if (!isAllowed) {
            alert(`File type not allowed. Please select a ZIP, PDF, or image file.`);
            fileInput.value = ""; // Clear the file input
            return;
          }

          // Reset previous preview
          filePreviewDiv.innerHTML = "";

          // Show file info
          const fileInfo = document.createElement("div");
          fileInfo.className = "file-info";
          fileInfo.innerHTML = `
            <strong>Selected file:</strong> ${file.name} (${formatFileSize(file.size)})

            <button class="delete-btn" onclick="clearFile()">Remove</button>
          `;
        filePreviewDiv.appendChild(fileInfo);

        // Show preview based on file type or extension
        if (file.type.startsWith("image/")) {
          // Image preview
          const reader = new FileReader();
          reader.onload = (e) => {
            const img = document.createElement("img");
            img.src = e.target.result;
            img.className = "image-preview";
            img.style.maxWidth = "200px";
            filePreviewDiv.appendChild(img);
          };
          reader.readAsDataURL(file);
        } else if (file.type === "application/pdf") {
          // PDF icon
          const pdfIcon = document.createElement("div");
          pdfIcon.innerHTML = `<div class="file-icon">📄 PDF Document</div>`;
          filePreviewDiv.appendChild(pdfIcon);
        } else if (
          file.type === "application/zip" ||
          file.type === "application/x-zip-compressed" ||
          file.type === "application/x-zip" ||
          (file.type === "application/octet-stream" &&
            fileExtension === "zip") ||
          fileExtension === "zip"
        ) {
          // ZIP icon - handle multiple possible MIME types
          const zipIcon = document.createElement("div");
          zipIcon.innerHTML = `<div class="file-icon">🗜️ ZIP Archive</div>`;
          filePreviewDiv.appendChild(zipIcon);
        }

        currentFile = file;
      }

      function clearFile() {
        fileInput.value = "";
        filePreviewDiv.innerHTML = "";
        currentFile = null;
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      }

      async function sendMessage() {
        const message = messageInput.value;

        let fileBase64 = null;
        let fileName = null;
        let fileType = null;
        let fileSize = null;

        if (currentFile) {
          // Double-check file size before processing
          if (currentFile.size > MAX_FILE_SIZE) {
            alert(
              "File size exceeds the 50 MB limit. Please select a smaller file."
            );
            clearFile();
            return;
          }


          currentFile = file;
        }

        function clearFile() {
          fileInput.value = "";
          filePreviewDiv.innerHTML = "";
          currentFile = null;
        }

        function formatFileSize(bytes) {
          if (bytes === 0) return "0 Bytes";
          const k = 1024;
          const sizes = ["Bytes", "KB", "MB", "GB"];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        }

        
        async function sendMessage() {
          const message = messageInput.value;

          // Check if connected
          if (!isConnected) {
            alert("Not connected to server. Please wait for connection.");
            return;
          }

          // Check if user is properly set up
          if (!currentUserId || !currentReceiverId || !currentPatientId) {
            alert("Please connect first and select a receiver.");
            return;
          }

          let fileBase64 = null;
          let fileName = null;
          let fileType = null;
          let fileSize = null;

          if (currentFile) {
            // Double-check file size before processing
            if (currentFile.size > MAX_FILE_SIZE) {
              alert("File size exceeds the 50 MB limit. Please select a smaller file.");
              clearFile();
              return;
            }
            
            // For ZIP files, ensure we're using a consistent MIME type
            let processedFileType = currentFile.type;
            const extension = currentFile.name.split('.').pop().toLowerCase();
            
            // If it's a ZIP file (by extension) but has an unusual MIME type, standardize it
            if (extension === 'zip' && 
                (currentFile.type === 'application/octet-stream' || 
                !['application/zip', 'application/x-zip-compressed', 'application/x-zip'].includes(currentFile.type))) {
              console.log("Standardizing ZIP file MIME type from", currentFile.type, "to application/zip");
              processedFileType = 'application/zip';
            }
            
            try {
              const base64 = await toBase64(currentFile);
              fileBase64 = base64;
              fileName = currentFile.name;
              fileType = processedFileType; // Use the standardized type
              fileSize = currentFile.size;
              
              console.log("File prepared for upload:", {
                name: fileName,
                type: fileType,
                size: formatFileSize(fileSize)
              });
            } catch (error) {
              console.error("Error converting file to base64:", error);
              alert("Failed to process the file. Please try again.");
              clearFile();
              return;
            }

          }

          try {
            const base64 = await toBase64(currentFile);
            fileBase64 = base64;
            fileName = currentFile.name;
            fileType = processedFileType; // Use the standardized type
            fileSize = currentFile.size;

            console.log("File prepared for upload:", {
              name: fileName,
              type: fileType,
              size: formatFileSize(fileSize),
            });
          } catch (error) {
            console.error("Error converting file to base64:", error);
            alert("Failed to process the file. Please try again.");
            clearFile();
            return;
          }
        }

        if (!message && !fileBase64) return;

        // Show loading indicator or disable send button here if needed


        // Socket event listeners
        socket.on("conversationsList", (data) => {
          console.log("Received conversations list:", data);
          conversations = data;
          renderConversations(data);
        });

        socket.on("messagesList", (data) => {
          console.log("Received messages list:", data);
          renderMessages(data);
        });

        socket.on("newMessage", (message) => {
          console.log("Received new message:", message);
          // Only show if it's for the current patient
          if (parseInt(message.patient_id) === parseInt(currentPatientId)) {
            const otherUser =
              message.sender_id == currentUserId
                ? message.receiver_id
                : message.sender_id;

            if (otherUser == currentReceiverId) {
              // If chat is open with this user, refresh messages
              socket.emit("getConversationMessages", {
                user_id: parseInt(currentUserId),
                target_user_id: otherUser,
                patient_id: parseInt(currentPatientId),
              });

              socket.emit("markMessagesAsRead", {
                user_id: parseInt(currentUserId),
                sender_id: otherUser,
                patient_id: parseInt(currentPatientId),
              });

              updateUnreadCount(otherUser, 0);
            } else {
              // Create new conversation
              conversations.unshift({
                message_id: message.id,
                message: message.message || `Sent a file: ${message.file_name}`,
                created_at: message.created_at,
                unread_count: 1,
                user: {
                  id: otherUser,
                  name: `${message.first_name} ${message.last_name}`,
                  profile_image: message.profile_image,
                },
              });
            }
            renderConversations(conversations);
          }
        }
      });

      socket.on("messageDeleted", ({ messageId }) => {
        socket.emit("getConversationMessages", {
          user_id: parseInt(currentUserId),
          target_user_id: parseInt(currentReceiverId),
          patient_id: parseInt(currentPatientId),
        });
      });


        // Add error handler for server-side validation failures
        socket.on("responseError", (errorMessage) => {
          console.error("Server error:", errorMessage);
          alert("Server Error: " + errorMessage);
        });

        // Add success handler for sent messages
        socket.on("messageSent", (data) => {
          console.log("Message sent successfully:", data);
        });


      function isFileTypeAllowed(fileType) {
        // Direct match with allowed types
        if (Object.keys(ALLOWED_FILE_TYPES).includes(fileType)) {
          return true;
        }

        // For octet-stream, we need to check the extension
        if (fileType === "application/octet-stream") {
          const extension = currentFile
            ? currentFile.name.split(".").pop().toLowerCase()
            : "";
          return extension === "zip";
        }

        return false;
      }
    </script>
  </body>
</html> -->
