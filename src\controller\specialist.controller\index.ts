import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import asyncHandler from "../../middlewares/trycatch";
import validate from "../../validations";
import {
  createUserSchema,
  createEmployeeSchema,
} from "../../validations/user.validation";
import { sendEmployeeCredentialEmail } from "../../utils/services/nodemailer/employeeCredential";
import { addressSchema } from "../../validations/address.validation";

// ---------------------------------------------
// Review (Approve or Reject) a submitted patient file (Specialist only)
// ---------------------------------------------
// export const reviewPatientFile = asyncHandler(
//   async (req: Request, res: Response) => {
//     try {
//       // 1) Only specialists can call this
//       if (!req.user || req.user.role !== UserRole.SPECIALIST) {
//         return sendResponse(res, 403, "Only specialists can review patient files", false);
//       }

//       // 2) Extract patientFileId from URL params
//       const { patientFileId } = req.params;
//       if (!patientFileId) {
//         return sendResponse(res, 400, "patientFileId is required", false);
//       }

//       // 3) Extract action from form-data (must be "approve" or "reject")
//       const { action } = req.body;
//       if (action !== "approve" && action !== "reject") {
//         return sendResponse(res, 400, "Action must be either 'approve' or 'reject'", false);
//       }

//       // 4) Verify that the patient file exists and is still "pending"
//       const existingFile = await db(TABLE.REFINEMENTS)
//         .where({ id: patientFileId })
//         .first();
//       if (!existingFile) {
//         return sendResponse(res, 404, "Patient file not found", false);
//       }
//       if (existingFile.status !== "pending") {
//         return sendResponse(res, 400, "Only files with status 'pending' can be reviewed", false);
//       }

//       // 5) Branch based on action
//       let updatedFile: any;
//       if (action === "approve") {
//         // 5a) Approve logic
//         [updatedFile] = await db(TABLE.REFINEMENTS)
//           .where({ id: patientFileId })
//           .update({
//             status: "approved",
//             rejection_reason: null,
//             rejection_pdf_url: null,
//             reviewed_at: new Date(),
//           })
//           .returning([
//             "id",
//             "doctor_id",
//             "status",
//             "rejection_reason",
//             "rejection_pdf_url",
//             "reviewed_at",
//           ]);

//         // (Optional) Notify doctor that the file was approved
//         // await sendNotificationToDoctor({
//         //   doctorId: updatedFile.doctor_id,
//         //   message: `Your patient #PID${updatedFile.id} file has been approved by the specialist.`,
//         //   data: { patientFileId: updatedFile.id },
//         // });

//         return sendResponse(
//           res,
//           200,
//           "Patient file approved successfully",
//           true,
//           { patientFile: updatedFile }
//         );
//       } else {
//         // 5b) Reject logic
//         //   Ensure rejection_reason is provided
//         const { rejection_reason } = req.body;
//         if (!rejection_reason || rejection_reason.trim().length === 0) {
//           return sendResponse(res, 400, "Rejection reason is required when action is 'reject'", false);
//         }

//         //   Handle uploaded PDF (if any)
//         let rejectionPdfUrl: string | null = null;
//         if (req.file) {
//           rejectionPdfUrl = req.file.path;
//         }

//         [updatedFile] = await db(TABLE.REFINEMENTS)
//           .where({ id: patientFileId })
//           .update({
//             status: "rejected",
//             rejection_reason: rejection_reason.trim(),
//             rejection_pdf_url: rejectionPdfUrl,
//             reviewed_at: new Date(),
//           })
//           .returning([
//             "id",
//             "doctor_id",
//             "status",
//             "rejection_reason",
//             "rejection_pdf_url",
//             "reviewed_at",
//           ]);

//         // (Optional) Notify doctor that the file was rejected
//         // await sendNotificationToDoctor({
//         //   doctorId: updatedFile.doctor_id,
//         //   message: `Your patient #PID${updatedFile.id} file has been rejected by the specialist.`,
//         //   data: { patientFileId: updatedFile.id },
//         // });

//         return sendResponse(
//           res,
//           200,
//           "Patient file rejected successfully",
//           true,
//           { patientFile: updatedFile }
//         );
//       }
//     } catch (error: any) {
//       console.error("reviewPatientFile error:", error);
//       return sendResponse(res, 500, error.message || "Internal server error", false);
//     }
//   }
// );
export const reviewPatientVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // 1) Only specialists can review
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists and its employee  can review versions",
          false
        );
      }

      // 2) Extract versionId
      const { versionId } = req.params;
      if (!versionId) {
        return sendResponse(res, 400, "versionId is required", false);
      }

      // 3) Extract action and optional fields
      const { action, reason, upper_steps, lower_steps, shared_link } =
        req.body;
      if (!["approve", "reject"].includes(action)) {
        return sendResponse(
          res,
          400,
          "Action must be 'approve' or 'reject'",
          false
        );
      }

      // 4) Fetch the version
      const version = await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .first();
      if (!version) {
        return sendResponse(res, 404, "Version not found", false);
      }

      // Ensure only doctor-sent versions are reviewed
      if (version.status !== "sent_by_doctor") {
        return sendResponse(
          res,
          400,
          "Only 'sent_by_doctor' versions can be reviewed",
          false
        );
      }

      // --- APPROVE FLOW ---
      if (action === "approve") {
        if (!shared_link) {
          return sendResponse(
            res,
            400,
            "Shared link is required to approve a version",
            false
          );
        }
        await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: versionId })
          .update({
            status: "approved_by_specialist",
            approval_reason: reason || null,
            upper_steps: upper_steps || null,
            lower_steps: lower_steps || null,
            shared_link,
            specialist_id: req.user.id, // <-- set specialist_id
          });

        return sendResponse(res, 200, "Version approved successfully", true);
      }

      // --- REJECT FLOW ---
      if (shared_link) {
        return sendResponse(
          res,
          400,
          "Cannot share a link when rejecting a version",
          false
        );
      }

      if (!reason) {
        return sendResponse(res, 400, "Rejection reason is required", false);
      }

      await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .update({
          status: "rejected_by_specialist",
          rejection_reason: reason,
          upper_steps: upper_steps || null,
          lower_steps: lower_steps || null,
          specialist_id: req.user.id, // <-- set specialist_id
        });

      return sendResponse(res, 200, "Version rejected successfully", true);
    } catch (error: any) {
      console.error("reviewPatientVersion error:", error);
      return sendResponse(
        res,
        500,
        error.message || "Internal server error",
        false
      );
    }
  }
);

export const getAllPatientsForSpecialist = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      if (
        !req.user ||
        (req.user.role !== UserRole.DOCTOR &&
          req.user.role !== UserRole.SPECIALIST)
      ) {
        return sendResponse(
          res,
          403,
          "Only doctors and specialists can access patient list",
          false
        );
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      const baseUrl = process.env.BASE_URL;
      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      let patientsQuery = db(TABLE.PATIENTS).orderBy("created_at", "desc");
      let totalCountQuery = db(TABLE.PATIENTS);
      if (req.user.role === UserRole.DOCTOR) {
        patientsQuery = patientsQuery.where({ doctor_id: req.user.id });
        totalCountQuery = totalCountQuery.where({ doctor_id: req.user.id });
      }

      const totalCountResult = await totalCountQuery
        .count({ count: "*" })
        .first();
      const total = Number(totalCountResult?.count || 0);
      const totalPages = Math.ceil(total / limit);

      const patients = await patientsQuery.limit(limit).offset(offset);

      const enrichedPatients = await Promise.all(
        patients.map(async (patient) => {
          let planName = null;
          if (patient.plan_id) {
            const plan = await db(TABLE.PLANS)
              .where({ id: patient.plan_id })
              .first();
            if (plan) planName = plan.name;
          }

          let clinicalConditions: string[] | null = null;
          if (patient.clinical_conditions) {
            try {
              clinicalConditions = JSON.parse(patient.clinical_conditions);
            } catch {
              clinicalConditions = null;
            }
          }

          const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
            .where({ patient_id: patient.id })
            .orderBy("version_number", "desc")
            .first();
          const patientStatus = latestVersion ? latestVersion.status : null;

          const enriched = {
            ...patient,
            plan_name: planName,
            clinical_conditions: clinicalConditions,
            status: patientStatus,
          };

          for (const field of fileFields) {
            if (patient[field]) {
              enriched[
                field
              ] = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${patient[field]}`;
            }
          }

          return enriched;
        })
      );

      return sendResponse(res, 200, "Patients fetched successfully", true, {
        data: enrichedPatients,
        pagination: {
          total,
          page,
          limit,
          totalPages,
        },
      });
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);

export const getPatientByIdForSpecialist = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const patientId = Number(req.params.id);
      if (!patientId) {
        return sendResponse(res, 400, "Patient ID is required", false);
      }

      // Only specialists may access this route
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists can access patient data",
          false
        );
      }

      // === CHANGE: remove doctor_id restriction so specialist can fetch any patient
      const patient = await db(TABLE.PATIENTS)
        .where({ id: patientId }) // ← doctor_id filter removed
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // 1b. Doctor info (get name)
      let doctorName = null;
      if (patient.doctor_id) {
        const doctor = await db(TABLE.USERS)
          .where({ id: patient.doctor_id })
          .select("first_name", "last_name")
          .first();
        if (doctor) {
          doctorName = `${doctor.first_name} ${doctor.last_name}`;
        }
      }

      // 1c. Specialist info (get name)
      let specialistName = null;
      let specialistId = null;
      // Try to get specialist_id from latestVersion or patient
      let specialist_id = null;

      // 2. Plan info
      let plan = null;
      if (patient.plan_id) {
        plan = await db(TABLE.PLANS).where({ id: patient.plan_id }).first();
      }

      // 3. Latest patient version status
      const latestVersion = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "desc")
        .first();
      const patientStatus = latestVersion ? latestVersion.status : null;

      if (latestVersion && latestVersion.specialist_id) {
        specialist_id = latestVersion.specialist_id;
      } else if (patient.specialist_id) {
        specialist_id = patient.specialist_id;
      }
      if (specialist_id) {
        specialistId = specialist_id;
        const specialist = await db(TABLE.USERS)
          .where({ id: specialist_id })
          .select("first_name", "last_name")
          .first();
        if (specialist) {
          specialistName = `${specialist.first_name} ${specialist.last_name}`;
        }
      }

      // 3b. All versions for this patient
      const allVersions = await db(TABLE.PATIENTS_VERSIONS)
        .where({ patient_id: patientId })
        .orderBy("version_number", "asc");

      // 4. Doctor addresses
      let shipToOffice = null,
        billToOffice = null;
      if (patient.ship_to_office) {
        shipToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.ship_to_office })
          .first();
      }
      if (patient.bill_to_office) {
        billToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.bill_to_office })
          .first();
      }

      // 5. Related tables
      const refinements = await db(TABLE.REFINEMENTS).where({
        patient_id: patientId,
      });
      const alignerReplacements = await db("aligner_replacements").where({
        patient_id: patientId,
      });
      const retainers = await db("four_d_graphy_retainers").where({
        patient_id: patientId,
      });
      const refinementsAligner = await db("refinements_aligner").where({
        patient_id: patientId,
      });
      const patientFiles = await db("patient_files").where({
        patient_id: patientId,
      });

      // 6. Parse clinical conditions
      let clinicalConditions: string[] | null = null;
      if (patient.clinical_conditions) {
        try {
          clinicalConditions = JSON.parse(patient.clinical_conditions);
        } catch {
          clinicalConditions = null;
        }
      }

      // 7. File URLs
      const baseUrl = process.env.BASE_URL;
      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      const enrichedPatient: any = {
        ...patient,
        clinical_conditions: clinicalConditions,
        plan,
        shipToOffice,
        billToOffice,
        refinements,
        alignerReplacements,
        retainers,
        refinementsAligner,
        patientFiles,
        status: patientStatus,
        versions: allVersions,
        doctor_id: patient.doctor_id,
        doctor_name: doctorName,
        specialist_id: specialistId,
        specialist_name: specialistName,
      };

      for (const field of fileFields) {
        if (patient[field]) {
          enrichedPatient[
            field
          ] = `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${patient[field]}`;
        }
      }

      // Enrich retainers
      enrichedPatient.retainers = retainers.map((r) => ({
        ...r,
        stl_file1: r.stl_file1
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.stl_file1}`
          : null,
        stl_file2: r.stl_file2
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.stl_file2}`
          : null,
        profile_repose: r.profile_repose
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.profile_repose}`
          : null,
        buccal_right: r.buccal_right
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccal_right}`
          : null,
        buccal_left: r.buccal_left
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccal_left}`
          : null,
        frontal_repose: r.frontal_repose
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontal_repose}`
          : null,
        frontal_smiling: r.frontal_smiling
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontal_smiling}`
          : null,
        labial_anterior: r.labial_anterior
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.labial_anterior}`
          : null,
        occlusal_lower: r.occlusal_lower
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlusal_lower}`
          : null,
        occlusal_upper: r.occlusal_upper
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlusal_upper}`
          : null,
      }));

      // Enrich refinementsAligner
      enrichedPatient.refinementsAligner = refinementsAligner.map((r) => ({
        ...r,
        upper_impression: r.upper_impression
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.upper_impression}`
          : null,
        lower_impression: r.lower_impression
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.lower_impression}`
          : null,
        profileRepose: r.profileRepose
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.profileRepose}`
          : null,
        buccalRight: r.buccalRight
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccalRight}`
          : null,
        buccalLeft: r.buccalLeft
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.buccalLeft}`
          : null,
        frontalRepose: r.frontalRepose
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontalRepose}`
          : null,
        frontalSmiling: r.frontalSmiling
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.frontalSmiling}`
          : null,
        labialAnterior: r.labialAnterior
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.labialAnterior}`
          : null,
        occlussalLower: r.occlussalLower
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlussalLower}`
          : null,
        occlussalUpper: r.occlussalUpper
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.occlussalUpper}`
          : null,
        radioGraph1: r.radioGraph1
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.radioGraph1}`
          : null,
        radioGraph2: r.radioGraph2
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${r.radioGraph2}`
          : null,
      }));

      // Enrich patientFiles
      enrichedPatient.patientFiles = patientFiles.map((f) => ({
        ...f,
        file_name: f.file_name
          ? `https://${process.env.BUCKET_NAME}.${process.env.REGION}.aliyuncs.com/${f.file_name}`
          : null,
      }));

      return sendResponse(
        res,
        200,
        "Patient fetched successfully",
        true,
        enrichedPatient
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);

// =====================================================
// SPECIALIST EMPLOYEE MANAGEMENT FUNCTIONS
// =====================================================

// Create employee under a specialist
export const createEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const body = { ...req.body };
      const validationResult = validate(createEmployeeSchema, body, res);
      if (!validationResult.success) {
        return;
      }

      let {
        first_name,
        last_name,
        email,
        username,
        salutation,
        practice_phone_number,
        mobile,
        profession,
      } = validationResult.data;

      // Check if specialist is authorized
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        sendResponse(res, 403, "Only specialists can create employees", false);
        return;
      }

      // Check if email already exists
      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // Auto-generate username from first_name and last_name
      let baseUsername = (first_name + last_name)
        .replace(/\s+/g, "")
        .toLowerCase();
      let candidate = baseUsername;
      let counter = 1;

      while (await db(TABLE.USERS).where({ username: candidate }).first()) {
        candidate = baseUsername + counter;
        counter++;
      }
      username = candidate;

      // Get the EMPLOYEE role ID
      const employeeRole = await db(TABLE.ROLES)
        .where("role_name", UserRole.EMPLOYEE)
        .first();

      if (!employeeRole) {
        sendResponse(res, 400, "Employee role not found", false);
        return;
      }
      const employeeRoleId = employeeRole.id;

      // Generate random password
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      const password = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(password, 10);

      // Generate 7-digit unique user_uuid
      let user_uuid: string;
      do {
        user_uuid = Math.floor(
          1_000_000 + Math.random() * 9_000_000
        ).toString();
      } while (await db(TABLE.USERS).where({ user_uuid }).first());

      console.log("🟠 Generated user_uuid for employee:", user_uuid);

      // Create employee user
      const newEmployee = await db(TABLE.USERS)
        .insert({
          user_uuid,
          first_name,
          last_name,
          email,
          username: username || null,
          password: hashedPassword,
          role_id: employeeRoleId,
          is_verified: true,
          is_active: true,
        })
        .returning([
          "id",
          "user_uuid",
          "first_name",
          "last_name",
          "email",
          "username",
          "role_id",
        ]);

      // Create specialist-employee relationship
      await db(TABLE.USER_EMPLOYEES).insert({
        supervisor_id: req.user.id,
        employee_id: newEmployee[0].id,
        supervisor_type: "specialist",
        salutation: salutation || null,
        practice_phone_number,
        mobile: mobile || null,
        profession: profession || null,
      });

      // Get role name for the response
      const role = await db(TABLE.ROLES).where("id", employeeRoleId).first();

      const responseData = {
        ...newEmployee[0],
        role: role.role_name,
        salutation: salutation || null,
        practice_phone_number,
        mobile: mobile || null,
        profession: profession || null,
      };

      // Send credentials email
      try {
        await sendEmployeeCredentialEmail({
          email,
          password,
          name: `${first_name} ${last_name}`,
          doctorName: `${req.user.first_name} ${req.user.last_name}`,
          salutation: salutation || undefined,
          practice_phone_number,
          mobile: mobile || undefined,
          profession: profession || undefined,
        });

        sendResponse(
          res,
          201,
          "Employee created successfully and credentials sent via email",
          true,
          responseData
        );
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        sendResponse(
          res,
          201,
          "Employee created successfully but failed to send email",
          true,
          responseData
        );
      }
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

// Get all employees under a specialist
export const getEmployees = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists can view their employees",
          false
        );
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = (page - 1) * limit;

      // Total count for pagination (excluding soft-deleted users)
      const totalEmployeesResult = await db(TABLE.USER_EMPLOYEES)
        .join(TABLE.USERS, `${TABLE.USER_EMPLOYEES}.employee_id`, "=", `${TABLE.USERS}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "specialist")
        .where(`${TABLE.USERS}.is_deleted`, false)
        .count("employee_id as count")
        .first();

      const totalEmployees = Number(totalEmployeesResult?.count || 0);

      // Get paginated employees (excluding soft-deleted users)
      const employees = await db(TABLE.USERS)
        .join(
          TABLE.USER_EMPLOYEES,
          `${TABLE.USERS}.id`,
          "=",
          `${TABLE.USER_EMPLOYEES}.employee_id`
        )
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "specialist")
        .where(`${TABLE.USERS}.is_deleted`, false)
        .orderBy(`${TABLE.USERS}.created_at`, "desc")
        .limit(limit)
        .offset(offset)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.username`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.USERS}.user_uuid`,
          `${TABLE.ROLES}.role_name as role`,
          `${TABLE.USER_EMPLOYEES}.salutation`,
          `${TABLE.USER_EMPLOYEES}.practice_phone_number`,
          `${TABLE.USER_EMPLOYEES}.mobile`,
          `${TABLE.USER_EMPLOYEES}.profession`,
          `${TABLE.USER_EMPLOYEES}.status`,
        ]);

      const formattedEmployees = employees.map((emp) => ({
        ...emp,
        profile_image: emp.profile_image
          ? process.env.BASE_URL + emp.profile_image
          : null,
        status: emp.status,
      }));

      sendResponse(res, 200, "Employees retrieved successfully", true, {
        data: formattedEmployees,
        currentPage: page,
        perPage: limit,
        totalItems: totalEmployees,
        totalPages: Math.ceil(totalEmployees / limit),
      });
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

// Get employee by ID for specialist
export const getEmployeeById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists can view their employees",
          false
        );
      }

      // Verify this employee belongs to the specialist
      const employee = await db(TABLE.USERS)
        .join(
          TABLE.USER_EMPLOYEES,
          `${TABLE.USERS}.id`,
          "=",
          `${TABLE.USER_EMPLOYEES}.employee_id`
        )
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_id`, req.user.id)
        .where(`${TABLE.USER_EMPLOYEES}.supervisor_type`, "specialist")
        .andWhere(`${TABLE.USERS}.id`, employeeId)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.username`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.ROLES}.role_name as role`,
          `${TABLE.USER_EMPLOYEES}.salutation`,
          `${TABLE.USER_EMPLOYEES}.practice_phone_number`,
          `${TABLE.USER_EMPLOYEES}.mobile`,
          `${TABLE.USER_EMPLOYEES}.profession`,
        ])
        .first();

      if (!employee) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }

      // Format profile image URL
      if (employee.profile_image) {
        employee.profile_image = process.env.BASE_URL + employee.profile_image;
      }

      return sendResponse(
        res,
        200,
        "Employee fetched successfully",
        true,
        employee
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);

// Update employee under a specialist
export const updateEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const employeeId = Number(req.params.employeeId);
      const {
        first_name,
        last_name,
        email,
        role_id,
        salutation,
        practice_phone_number,
        mobile,
        profession,
      } = req.body;

      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists can update their employees",
          false
        );
      }
      const specialistId = req.user.id;

      // Verify relationship
      const rel = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: specialistId,
          employee_id: employeeId,
          supervisor_type: "specialist",
        })
        .first();
      if (!rel) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }

      // Fetch current employee data
      const current = await db(TABLE.USERS).where("id", employeeId).first();
      if (!current) {
        return sendResponse(res, 404, "Employee record not found", false);
      }

      // Check email duplicate if changed
      if (email && email !== current.email) {
        const dup = await db(TABLE.USERS)
          .where({ email })
          .whereNot("id", employeeId)
          .first();
        if (dup) {
          return sendResponse(res, 400, "Email already exists", false);
        }
      }

      // Build update payload
      const updateData: any = {};
      if (first_name) updateData.first_name = first_name;
      if (last_name) updateData.last_name = last_name;
      if (email) updateData.email = email;

      // Handle role change
      if (role_id) {
        const roleExists = await db(TABLE.ROLES).where("id", role_id).first();
        if (!roleExists) {
          return sendResponse(res, 400, "Invalid role ID", false);
        }
        if (
          [
            UserRole.ADMIN,
            UserRole.SUPERADMIN,
            UserRole.DOCTOR,
            UserRole.SPECIALIST,
          ].includes(roleExists.role_name as any)
        ) {
          return sendResponse(
            res,
            403,
            "You cannot assign this role to an employee",
            false
          );
        }
        updateData.role_id = role_id;
      }

      if (
        Object.keys(updateData).length === 0 &&
        !salutation &&
        !practice_phone_number &&
        !mobile &&
        !profession
      ) {
        return sendResponse(res, 400, "No fields to update", false);
      }

      // Perform update on users table
      let updated: any = current;
      if (Object.keys(updateData).length > 0) {
        const updatedRows = await db(TABLE.USERS)
          .where("id", employeeId)
          .update(updateData)
          .returning([
            "id",
            "first_name",
            "last_name",
            "email",
            "username",
            "role_id",
          ]);
        updated = updatedRows[0];
      }

      // Update user_employees table if any of the extra fields are present
      const userEmployeeUpdate: any = {};
      if (salutation !== undefined) userEmployeeUpdate.salutation = salutation;
      if (practice_phone_number !== undefined)
        userEmployeeUpdate.practice_phone_number = practice_phone_number;
      if (mobile !== undefined) userEmployeeUpdate.mobile = mobile;
      if (profession !== undefined) userEmployeeUpdate.profession = profession;

      if (Object.keys(userEmployeeUpdate).length > 0) {
        await db(TABLE.USER_EMPLOYEES)
          .where({
            supervisor_id: specialistId,
            employee_id: employeeId,
            supervisor_type: "specialist",
          })
          .update(userEmployeeUpdate);
      }

      const role = await db(TABLE.ROLES).where("id", updated.role_id).first();

      // Fetch latest user_employees data for response
      const userEmp = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: specialistId,
          employee_id: employeeId,
          supervisor_type: "specialist",
        })
        .first();

      return sendResponse(res, 200, "Employee updated successfully", true, {
        ...updated,
        role: role.role_name,
        ...userEmp,
      });
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);

// Delete an employee under a specialist
export const deleteEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        sendResponse(
          res,
          403,
          "Only specialists can delete their employees",
          false
        );
        return;
      }

      const specialistId = req.user.id;

      // Verify this employee belongs to the specialist
      const relationship = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: specialistId,
          employee_id: employeeId,
          supervisor_type: "specialist",
        })
        .first();

      if (!relationship) {
        sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
        return;
      }

      // Begin transaction
      await db.transaction(async (trx) => {
        // First remove the relationship
        await trx(TABLE.USER_EMPLOYEES)
          .where({
            supervisor_id: specialistId,
            employee_id: employeeId,
            supervisor_type: "specialist",
          })
          .delete();

        // Then soft delete the user instead of hard delete
        await trx(TABLE.USERS)
          .where("id", employeeId)
          .update({ 
            is_deleted: true,
            updated_at: new Date()
          });
      });

      sendResponse(res, 200, "Employee deleted successfully", true);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

// Update employee status under a specialist
export const updateEmployeeStatus = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const employeeId = Number(req.params.employeeId);
      const { status } = req.body;
      const allowed = ["active", "inactive"];
      if (!status || !allowed.includes(status)) {
        return sendResponse(
          res,
          400,
          `Status must be one of: ${allowed.join(", ")}`,
          false
        );
      }
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(
          res,
          403,
          "Only specialists can update employee status",
          false
        );
      }
      const specialistId = req.user.id;
      // Verify relationship
      const rel = await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: specialistId,
          employee_id: employeeId,
          supervisor_type: "specialist",
        })
        .first();
      if (!rel) {
        return sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
      }
      // Update status
      await db(TABLE.USER_EMPLOYEES)
        .where({
          supervisor_id: specialistId,
          employee_id: employeeId,
          supervisor_type: "specialist",
        })
        .update({ status });
      return sendResponse(
        res,
        200,
        "Employee status updated successfully",
        true,
        { employeeId, status }
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
