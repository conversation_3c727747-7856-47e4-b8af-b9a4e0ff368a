import OSS from "../../../config/oss-config";
import { v4 as uuidv4 } from "uuid";
import sharp from "sharp";
import { appLogger, errorLogger } from "../../../config/logger";

interface CompressionResult {
  buffer: Buffer;
  size: number;
  format: string;
}

interface UploadResult {
  key: string;
  url: string;
  publicUrl: string;
}

// Image compression settings
const COMPRESSION_SETTINGS = {
  jpeg: { quality: 85, progressive: true },
  png: { compressionLevel: 8, progressive: true },
  webp: { quality: 85, effort: 4 },
  gif: { colors: 256 },
};

// Maximum dimensions for chat images
const MAX_WIDTH = 1920;
const MAX_HEIGHT = 1080;

// Compress image using Sharp

export const compressImage = async (
  buffer: Buffer,
  mimeType: string
): Promise<CompressionResult> => {
  try {
    let sharpInstance = sharp(buffer);

    // Get image metadata
    const metadata = await sharpInstance.metadata();
    const { width = 0, height = 0, format } = metadata;

    // Resize if image is too large
    if (width > MAX_WIDTH || height > MAX_HEIGHT) {
      sharpInstance = sharpInstance.resize(MAX_WIDTH, MAX_HEIGHT, {
        fit: "inside",
        withoutEnlargement: true,
      });
    }

    // Apply compression based on format
    let compressedBuffer: Buffer;
    let outputFormat = format || "jpeg";

    switch (mimeType) {
      case "image/jpeg":
      case "image/jpg":
        compressedBuffer = await sharpInstance
          .jpeg(COMPRESSION_SETTINGS.jpeg)
          .toBuffer();
        outputFormat = "jpeg";
        break;

      case "image/png":
        compressedBuffer = await sharpInstance
          .png(COMPRESSION_SETTINGS.png)
          .toBuffer();
        outputFormat = "png";
        break;

      case "image/webp":
        compressedBuffer = await sharpInstance
          .webp(COMPRESSION_SETTINGS.webp)
          .toBuffer();
        outputFormat = "webp";
        break;

      case "image/gif":
        // Convert GIF to PNG for better compression
        compressedBuffer = await sharpInstance
          .png(COMPRESSION_SETTINGS.png)
          .toBuffer();
        outputFormat = "png";
        break;

      default:
        // Fallback to JPEG
        compressedBuffer = await sharpInstance
          .jpeg(COMPRESSION_SETTINGS.jpeg)
          .toBuffer();
        outputFormat = "jpeg";
    }

    return {
      buffer: compressedBuffer,
      size: compressedBuffer.length,
      format: outputFormat,
    };
  } catch (error: any) {
    errorLogger.error("Image compression failed", {
      error: error.message,
      mimeType,
      originalSize: buffer.length,
    });

    // Return original buffer if compression fails
    return {
      buffer,
      size: buffer.length,
      format: mimeType.split("/")[1] || "jpeg",
    };
  }
};

// Upload image to OSS with optimized settings

export const uploadImageToOSS = async (
  buffer: Buffer,
  originalName: string,
  mimeType: string,
  folder = "chat"
): Promise<UploadResult> => {
  try {
    // Generate unique filename
    const timestamp = Date.now();
    const uuid = uuidv4();
    const ext = originalName.split(".").pop()?.toLowerCase() || "jpg";
    const fileName = `${folder}/${timestamp}-${uuid}.${ext}`;

    // Set appropriate content type
    const contentType = mimeType.startsWith("image/") ? mimeType : "image/jpeg";

    // Upload to OSS with optimized headers
    const result = await OSS.put(fileName, buffer, {
      // headers: {
      //   "Content-Type": contentType,
      //   "Cache-Control": "public, max-age=31536000",
      //   "x-oss-object-acl": "public-read",
      //   "x-oss-storage-class": "Standard",
      // },
      headers: { "Content-Type": mimeType, "x-oss-object-acl": "public-read" },

      timeout: 60000, // 60 second timeout
    });

    // Generate public URL (without signed URL for public images)
    const publicUrl = fileName;

    return {
      key: fileName,
      url: result.url,
      publicUrl,
    };
  } catch (error: any) {
    errorLogger.error("OSS upload failed", {
      error: error.message,
      fileName: originalName,
      mimeType,
      folder,
      bufferSize: buffer.length,
    });
    throw new Error(`Failed to upload image to OSS: ${error.message}`);
  }
};

// Generate signed URL for private images

export const generateSignedImageUrl = async (
  key: string,
  expiresInSeconds = 3600
): Promise<string> => {
  try {
    const decodedKey = decodeURIComponent(key);
    const signedUrl = await OSS.signatureUrl(decodedKey, {
      expires: expiresInSeconds,
      method: "GET",
    });

    return signedUrl;
  } catch (error: any) {
    errorLogger.error("Failed to generate signed URL", {
      error: error.message,
      key,
    });
    throw new Error(`Failed to generate signed URL: ${error.message}`);
  }
};

//  Delete image from OSS

export const deleteImageFromOSS = async (key: string): Promise<void> => {
  try {
    const decodedKey = decodeURIComponent(key);
    await OSS.delete(decodedKey);

    appLogger.info("Image deleted from OSS", { key: decodedKey });
  } catch (error: any) {
    errorLogger.error("Failed to delete image from OSS", {
      error: error.message,
      key,
    });
    throw new Error(`Failed to delete image: ${error.message}`);
  }
};

//  Validate image file

export const validateImageFile = (
  buffer: Buffer,
  mimeType: string,
  maxSize: number = 50 * 1024 * 1024
): { isValid: boolean; error?: string } => {
  // Check file size
  if (buffer.length > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`,
    };
  }

  // Check MIME type
  const allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  if (!allowedTypes.includes(mimeType)) {
    return {
      isValid: false,
      error: "Invalid image format. Only JPEG, PNG, GIF, and WebP are allowed.",
    };
  }

  // Additional validation using Sharp (checks if it's actually an image)
  try {
    sharp(buffer);
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: "Invalid image file or corrupted data",
    };
  }
};
