import { Request, Response, NextFunction } from 'express';
import { errorLogger } from '../config/logger';

export const globalErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    endpoint: req.originalUrl,
    method: req.method,
    ip: req.headers['x-forwarded-for'] as string || req.ip || 'unknown',
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    body: req.body,
    params: req.params,
    query: req.query,
    timestamp: new Date().toISOString()
  };

  errorLogger.error('Unhandled application error', errorInfo);

  // Don't expose internal errors in production
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : error.message;

  res.status(error.status || 500).json({
    status: error.status || 500,
    success: false,
    message
  });
};

export const handleUncaughtException = () => {
  process.on('uncaughtException', (error) => {
    errorLogger.error('Uncaught Exception', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
    process.exit(1);
  });
};

export const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (reason, promise) => {
    errorLogger.error('Unhandled Promise Rejection', {
      reason: reason,
      promise: promise,
      timestamp: new Date().toISOString()
    });
  });
};