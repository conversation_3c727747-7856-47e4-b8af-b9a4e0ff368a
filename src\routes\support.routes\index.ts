import { Router } from "express";
import { getAllSupportTickets, sendMailToSupport } from "../../controller/support.controller";
import { authAdminMiddleware } from "../../middlewares/authAdminMiddleware";
import upload from "../../middlewares/upload";

const router = Router();

router.post("/request", upload.none(),  sendMailToSupport);
router.get("/tickets", authAdminMiddleware, getAllSupportTickets);




export default router;
