// src/validations/supportSchema.ts
import { z } from "zod";

export const supportSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters long" })
    .max(100, { message: "Name must not exceed 100 characters" }),

  message: z
    .string()
    .min(5, { message: "Message must be at least 5 characters long" }),

  email: z
    .string()
    .email({ message: "Invalid email format" }),

  phone_number: z
    .string()
    .regex(/^\+?[1-9]\d{1,14}$/, {
      message: "Invalid phone number format",
    }),
});
