import { Router } from "express";
import { authMiddleware } from "../../middlewares/authMiddleware";
import {
  createEmployee,
  deleteEmployee,
  getEmployees,
  updateEmployee,
  addDoctorAddress,
  getDoctorAddresses,
  updateDoctorAddress,
  deleteDoctorAddress,
  savePatientStep,
  getPatientById,
  getAllPatientsForDoctor,
  uploadCbctFile,
  uploadRefinements,
  createAlignerReplacement,
  requestRetainer,
  reviewSharedLinkVersion,
  getAllPlans,
  getPlanById,
  getDoctorAddressById,
  getEmployeeById,
  updateEmployeeStatus,
  getSpecialistForPatient,
  updatePatientStatus,
  createUpdatesVersion,
  statusUpdateById,
} from "../../controller/doctor.controller";
import upload from "../../middlewares/upload";

const router = Router();

// Employee management routes
router.post("/employees", authMiddleware, upload.none(), createEmployee);
router.get("/employees", authMiddleware, getEmployees);
router.get("/employee/:employeeId", authMiddleware, getEmployeeById);
router.put(
  "/employees/:employeeId",
  authMiddleware,
  upload.none(),
  updateEmployee
);
router.delete("/employees/:employeeId", authMiddleware, deleteEmployee);
router.patch(
  "/employees/:employeeId",
  authMiddleware,
  upload.none(),
  updateEmployeeStatus
);

// Address management routes
router.post("/addresses", authMiddleware, upload.none(), addDoctorAddress);
router.get("/addresses", authMiddleware, getDoctorAddresses);
router.get("/addresses/:addressId", authMiddleware, getDoctorAddressById);
router.put(
  "/addresses/:addressId",
  authMiddleware,
  upload.none(),
  updateDoctorAddress
);
router.delete("/addresses/:addressId", authMiddleware, deleteDoctorAddress);

router.post(
  "/patients",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    { name: "cbctFile", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
    { name: "radioGraph1", maxCount: 1 },
    { name: "radioGraph2", maxCount: 1 },
    { name: "generalRecords", maxCount: 10 },
  ]),
  savePatientStep
);

router.post(
  "/retainer/patients",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    // { name: "cbctFile", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },


  ]),
  savePatientStep
);

router.post(
  "/patients/:patientId/cbct",
  authMiddleware,
  upload.fields([{ name: "cbctFile", maxCount: 1 }]),
  uploadCbctFile
);

router.post(
  "/patients/:patientId/refinements",
  authMiddleware,
  upload.fields([
    { name: "upperImpression", maxCount: 1 },
    { name: "lowerImpression", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
    { name: "radioGraph1", maxCount: 1 },
    { name: "radioGraph2", maxCount: 1 },
  ]),
  uploadRefinements
);
router.post(
  "/patients/aligner-replacements",
  authMiddleware,
  upload.none(),
  createAlignerReplacement
);

router.post(
  "/patients/4dgraphic_retainer",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
  ]),
  requestRetainer
);
router.get("/patients/:id", authMiddleware, upload.none(), getPatientById);
router.get(
  "/patients/:id/specialist",
  authMiddleware,
  upload.none(),
  getSpecialistForPatient
);
router.get("/patients", authMiddleware, upload.none(), getAllPatientsForDoctor);
router.post(
  "/patients/shared-review/:versionId",
  authMiddleware,
  upload.none(),
  reviewSharedLinkVersion
);
router.get("/plans", authMiddleware, upload.none(), getAllPlans);
router.get("/plan/:id", authMiddleware, upload.none(), getPlanById);
router.put("/patients/:patientId/status", authMiddleware, updatePatientStatus);
router.post("/patient/:patientId/version", authMiddleware, upload.none(), createUpdatesVersion);
router.post("/patient/status-update/:id", authMiddleware, upload.none(), statusUpdateById);



export default router;
