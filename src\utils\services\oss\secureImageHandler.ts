import {
  generateSignedImageUrl,
  deleteImageFromOSS,
} from "./imageUploadHelper";
import db from "../../../config/db";
import { TABLE } from "../../Database/table";

interface ImageAccessControl {
  userId: number;
  patientId: number;
  imageKey: string;
}

// Check if user has access to view an image
export const checkImageAccess = async (
  userId: number,
  patientId: number,
  messageId: number
): Promise<boolean> => {
  try {
    const message = await db(TABLE.CHATS)
      .select("sender_id", "receiver_id", "patient_id")
      .where("id", messageId)
      .first();

    if (!message) return false;

    // User can access if they are sender, receiver, and patient matches
    const hasAccess =
      (message.sender_id === userId || message.receiver_id === userId) &&
      message.patient_id === patientId;

    return hasAccess;
  } catch (error: any) {
    console.log(`Error checking image access: ${error.message}`);
    return false;
  }
};

// Get secure image URL with access control

export const getSecureImageUrl = async (
  userId: number,
  patientId: number,
  messageId: number,
  expiresInSeconds = 3600
): Promise<string | null> => {
  try {
    // Check access permissions
    const hasAccess = await checkImageAccess(userId, patientId, messageId);
    if (!hasAccess) {
      return null;
    }

    // Get message with file info
    const message = await db(TABLE.CHATS)
      .select("file_url", "file_name", "file_type")
      .where("id", messageId)
      .first();

    if (!message || !message.file_url) return null;

    // Extract key from URL if it's a full URL
    let imageKey = message.file_url;
    if (imageKey.includes("aliyuncs.com/")) {
      imageKey = imageKey.split("aliyuncs.com/")[1];
    }

    // Generate signed URL
    const signedUrl = await generateSignedImageUrl(imageKey, expiresInSeconds);

    return signedUrl;
  } catch (error: any) {
    console.log(`Error generating secure image URL: ${error.message}`);
    return null;
  }
};

// export const cleanupOrphanedImages = async (): Promise<void> => {
//   try {
//     const orphanedMessages = await db(TABLE.CHATS)
//       .select("file_url")
//       .where("file_url", "is not", null)
//       .where("created_at", "<", db.raw("NOW() - INTERVAL '30 days'"))
//       .whereNull("message")
//       .limit(100);

//     for (const message of orphanedMessages) {
//       if (message.file_url) {
//         try {
//           let imageKey = message.file_url;
//           if (imageKey.includes("aliyuncs.com/")) {
//             imageKey = imageKey.split("aliyuncs.com/")[1];
//           }

//           await deleteImageFromOSS(imageKey);
//           appLogger.info("Orphaned image cleaned up", { imageKey });
//         } catch (deleteError: any) {
//           errorLogger.error("Failed to delete orphaned image", {
//             error: deleteError.message,
//             fileUrl: message.file_url,
//           });
//         }
//       }
//     }
//   } catch (error: any) {
//     errorLogger.error("Cleanup orphaned images failed", {
//       error: error.message,
//     });
//   }
// };
