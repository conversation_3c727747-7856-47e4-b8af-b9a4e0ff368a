import { Request, Response } from "express";
import { Server, Socket } from "socket.io";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import asyncHandler from "../../middlewares/trycatch";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import { getIO } from "../../config/socket";

interface NotificationDetails {
  type?: string;
  shipment_id?: number;
  container_id?: string;
  driver_id?: number;
  driver_name?: string;
  [key: string]: any; // Allow for any other properties
}

// Add a map to track last connection time for users
let userLastSeen: Map<string, Date> = new Map();

export let users: {
  userId: string;
  socketIds: string[]; // Support multiple connections per user
}[] = [];

export const socketSetup = (io: Server) => {
  let users: Array<{ userId: string; socketIds: string[] }> = [];

  // Helper function to format notifications consistently with all details
  const formatNotification = (notification: any) => {
    let details: NotificationDetails = {};

    // Check if details is already an object or a string that needs parsing
    if (notification.details) {
      if (typeof notification.details === "string") {
        try {
          details = JSON.parse(notification.details) as NotificationDetails;
        } catch (e) {
          console.error("Error parsing notification details:", e);
        }
      } else if (typeof notification.details === "object") {
        details = notification.details as NotificationDetails;
      }
    }

    // Ensure all notifications have the same detailed format
    return {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      type: details.type || "general",
      container_id: details.container_id || null,
      shipment_id: details.shipment_id || null,
      tracking_number: details.tracking_number || null,
      pickup_location: details.pickup_location || null,
      delivery_address: details.delivery_address || null,
      timestamp: notification.created_at,
      is_read: !!notification.is_read, // Use double negation to ensure boolean conversion
    };
  };

  io.on("connection", (socket: Socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Get userId from query params
    const userId = socket.handshake.query.userId as string;

    if (userId) {
      // Store userId in socket data for later use
      socket.data.userId = userId;

      // Check if this user already has active connections
      const existingUser = users.find((user) => user.userId === userId);
      if (existingUser) {
        console.log(
          `User ${userId} already has ${existingUser.socketIds.length} active connections`
        );

        // Check if this socket ID is already in the list
        if (!existingUser.socketIds.includes(socket.id)) {
          console.log(`Adding new socket ID ${socket.id} for user ${userId}`);
          existingUser.socketIds.push(socket.id);
        } else {
          console.log(
            `Socket ID ${socket.id} already exists for user ${userId}`
          );
        }
      } else {
        console.log(`Adding new user ${userId} with socket ID ${socket.id}`);
        users.push({ userId, socketIds: [socket.id] });
      }

      // Emit the updated users list
      io.emit("getUsers", users);
    }

    // Handle notification event without pagination
    socket.on("notification", async () => {
      const userId = socket.data.userId;
      if (!userId) {
        socket.emit(
          "responseError",
          "User ID not found in socket data. Make sure you've called addUser first."
        );
        return;
      }

      try {
        // Get all notifications for this user - FORCE REFRESH FROM DATABASE
        const notifications = await db(TABLE.NOTIFICATIONS)
          .where({ receiver_id: userId })
          .orderBy("created_at", "desc");

        // Format notifications consistently with proper boolean conversion
        const formattedNotifications = notifications.map(
          (notification: any) => {
            let details: any = {};

            if (notification.details) {
              if (typeof notification.details === "string") {
                try {
                  details = JSON.parse(notification.details);
                } catch (e) {
                  console.error("Error parsing notification details:", e);
                }
              } else if (typeof notification.details === "object") {
                details = notification.details;
              }
            }

            // Ensure is_read is properly converted to boolean
            // PostgreSQL might return true/false directly or 1/0
            const isRead =
              typeof notification.is_read === "boolean"
                ? notification.is_read
                : notification.is_read === 1 || notification.is_read === true;

            return {
              id: notification.id,
              title: notification.title,
              message: notification.message,
              created_at: notification.created_at,
              is_read: isRead,
              ...details,
            };
          }
        );

        // Get unread count
        const unreadCountResult = await db(TABLE.NOTIFICATIONS)
          .where({ receiver_id: userId, is_read: false })
          .count("id as count")
          .first();
        const unreadCount =
          parseInt((unreadCountResult as any)?.count as string) || 0;

        // Send all notifications with unread count
        socket.emit("notification", {
          notifications: formattedNotifications,
          unread_count: unreadCount,
        });
      } catch (error) {
        console.error("Error fetching notifications:", error);
        socket.emit("responseError", "Failed to fetch notifications");
      }
    });

    socket.on("addUser", async (userId: string) => {
      console.log(
        `addUser event received for user ${userId} on socket ${socket.id}`
      );

      // Store userId in socket.data for direct access
      socket.data.userId = userId;

      // Check if this user already exists in our users array
      const existingUser = users.find((user) => user.userId === userId);
      if (existingUser) {
        // Check if this socket ID is already in the list
        if (!existingUser.socketIds.includes(socket.id)) {
          console.log(
            `Adding socket ID ${socket.id} to existing user ${userId}`
          );
          existingUser.socketIds.push(socket.id);
        } else {
          console.log(
            `Socket ID ${socket.id} already exists for user ${userId}`
          );
        }
      } else {
        console.log(
          `Creating new user entry for ${userId} with socket ID ${socket.id}`
        );
        users.push({ userId, socketIds: [socket.id] });
      }

      // Emit the updated users list to all connected clients
      io.emit("getUsers", users);

      try {
        // Get all notifications for this user - FORCE REFRESH FROM DATABASE
        const allNotifications = await db(TABLE.NOTIFICATIONS)
          .where({ receiver_id: userId })
          .orderBy("created_at", "desc");

        // Format notifications with proper boolean conversion
        const formattedNotifications = allNotifications.map(
          (notification: any) => {
            let details: any = {};

            // Check if details is already an object or a string that needs parsing
            if (notification.details) {
              if (typeof notification.details === "string") {
                try {
                  details = JSON.parse(notification.details);
                } catch (e) {
                  console.error("Error parsing notification details:", e);
                }
              } else if (typeof notification.details === "object") {
                details = notification.details;
              }
            }

            // Ensure is_read is properly converted to boolean
            // PostgreSQL might return true/false directly or 1/0
            const isRead =
              typeof notification.is_read === "boolean"
                ? notification.is_read
                : notification.is_read === 1 || notification.is_read === true;

            return {
              id: notification.id,
              title: notification.title,
              message: notification.message,
              created_at: notification.created_at,
              is_read: isRead, // Use the properly converted boolean
              ...details,
            };
          }
        );

        console.log(
          `Sending ${formattedNotifications.length} notifications to user ${userId}`
        );

        // Send all notifications to the user
        socket.emit("notification", formattedNotifications);
      } catch (error) {
        console.error("Error fetching all notifications:", error);
        socket.emit("responseError", "Failed to fetch notifications");
      }
    });

    socket.on("disconnect", () => {
      // Find the user who disconnected
      const userId = socket.data.userId;
      if (userId) {
        // Update last seen time on disconnect
        userLastSeen.set(userId, new Date());
      }

      users = users
        .map((user) => ({
          ...user,
          socketIds: user.socketIds.filter((id) => id !== socket.id),
        }))
        .filter((user) => user.socketIds.length > 0);

      // Emit the updated users list to all connected clients
      io.emit("getUsers", users);
    });
  });
};

/**
 * Get notifications for a user
 */
export const getNotifications = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      // Get pagination parameters
      const page = parseInt((req.query.page as string) || "1", 10);
      const limit = parseInt((req.query.limit as string) || "10", 10);
      const offset = (page - 1) * limit;

      // Get filter parameters
      const filterType = (req.query.filter_type as string) || "all";

      // Build query
      let query = db(TABLE.NOTIFICATIONS)
        .where({ receiver_id: userId })
        .orderBy("created_at", "desc")
        .offset(offset)
        .limit(limit);

      if (filterType === "unread") {
        query = query.andWhere({ is_read: false });
      }

      // Get total count for pagination
      const countQuery = db(TABLE.NOTIFICATIONS)
        .where({ receiver_id: userId })
        .count("id as count");

      if (filterType === "unread") {
        countQuery.andWhere({ is_read: false });
      }

      // Execute both queries
      const [notifications, totalCountResult] = await Promise.all([
        query,
        countQuery.first(),
      ]);

      // Format notifications
      const formattedNotifications = (notifications as any[]).map(
        (notification: any) => {
          let details: any = {};
          if (typeof notification.details === "string") {
            try {
              details = JSON.parse(notification.details);
            } catch (e) {
              console.error("Error parsing notification details:", e);
            }
          } else if (typeof notification.details === "object") {
            details = notification.details;
          }

          return {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            created_at: notification.created_at,
            is_read: !!notification.is_read, // Convert to boolean consistently
            ...details,
          };
        }
      );

      // Group notifications by date
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const thisWeekStart = new Date(today);
      thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay());

      const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);

      const groupedNotifications: {
        today: any[];
        yesterday: any[];
        this_week: any[];
        this_month: any[];
        older: any[];
      } = {
        today: [],
        yesterday: [],
        this_week: [],
        this_month: [],
        older: [],
      };

      formattedNotifications.forEach((notification: any) => {
        const notificationDate = new Date(notification.created_at);
        notificationDate.setHours(0, 0, 0, 0);

        if (notificationDate.getTime() === today.getTime()) {
          groupedNotifications.today.push(notification);
        } else if (notificationDate.getTime() === yesterday.getTime()) {
          groupedNotifications.yesterday.push(notification);
        } else if (notificationDate >= thisWeekStart) {
          groupedNotifications.this_week.push(notification);
        } else if (notificationDate >= thisMonthStart) {
          groupedNotifications.this_month.push(notification);
        } else {
          groupedNotifications.older.push(notification);
        }
      });

      const totalCount =
        parseInt((totalCountResult as any)?.count as string) || 0;

      return sendResponse(
        res,
        200,
        "Notifications fetched successfully",
        true,
        {
          notifications: groupedNotifications,
          pagination: {
            total: totalCount,
            page,
            limit,
            pages: Math.ceil(totalCount / limit),
          },
          unread_count: await db(TABLE.NOTIFICATIONS)
            .where({ receiver_id: userId, is_read: false })
            .count("id as count")
            .first()
            .then((result: any) => parseInt(result?.count as string) || 0),
        }
      );
    } catch (error: any) {
      console.error("Error fetching notifications:", error);
      return sendResponse(
        res,
        500,
        "Internal server error",
        false,
        error.message
      );
    }
  }
);

/**
 * Mark a notification as read
 */
export const markNotificationAsRead = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      const { notification_id } = req.params;

      // Check if notification exists and belongs to the user
      const notification = await db(TABLE.NOTIFICATIONS)
        .where({ id: notification_id, receiver_id: userId })
        .first();

      if (!notification) {
        return sendResponse(res, 404, "Notification not found", false);
      }

      // Update notification
      await db(TABLE.NOTIFICATIONS)
        .where({ id: notification_id })
        .update({ is_read: true });

      return sendResponse(res, 200, "Notification marked as read", true);
    } catch (error: any) {
      console.error("Error marking notification as read:", error);
      return sendResponse(
        res,
        500,
        "Internal server error",
        false,
        error.message
      );
    }
  }
);

/**
 * Mark all notifications as read
 */
export const markAllNotificationsAsRead = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      // Update all notifications
      await db(TABLE.NOTIFICATIONS)
        .where({ receiver_id: userId, is_read: false })
        .update({ is_read: true });

      return sendResponse(res, 200, "All notifications marked as read", true);
    } catch (error: any) {
      console.error("Error marking all notifications as read:", error);
      return sendResponse(
        res,
        500,
        "Internal server error",
        false,
        error.message
      );
    }
  }
);

/**
 * Force refresh notifications for a user
 */
export const refreshNotifications = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return sendResponse(res, 401, "Unauthorized", false);
      }

      // Get all notifications for this user
      const notifications = await db(TABLE.NOTIFICATIONS)
        .where({ receiver_id: userId })
        .orderBy("created_at", "desc");

      // Format notifications
      const formattedNotifications = (notifications as any[]).map(
        (notification: any) => {
          let details: any = {};
          if (typeof notification.details === "string") {
            try {
              details = JSON.parse(notification.details);
            } catch (e) {
              console.error("Error parsing notification details:", e);
            }
          } else if (typeof notification.details === "object") {
            details = notification.details;
          }

          return {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            created_at: notification.created_at,
            is_read: !!notification.is_read, // Convert to boolean consistently
            ...details,
          };
        }
      );

      // Get unread count
      const unreadCount =
        (await db(TABLE.NOTIFICATIONS)
          .where({ receiver_id: userId, is_read: false })
          .count("id as count")
          .first()
          .then((result: any) => parseInt(result?.count as string) || 0)) || 0;

      // Emit to all sockets for this user
      const io = getIO();
      const allSockets = Array.from(io.sockets.sockets.values());

      for (const socket of allSockets) {
        const socketUserId = socket.data.userId;
        if (socketUserId === String(userId)) {
          socket.emit("notification", {
            notifications: formattedNotifications,
            unread_count: unreadCount,
          });
        }
      }

      return sendResponse(
        res,
        200,
        "Notifications refreshed successfully",
        true,
        {
          unread_count: unreadCount,
        }
      );
    } catch (error) {
      console.error("Error refreshing notifications:", error);
      return sendResponse(res, 500, "Failed to refresh notifications", false);
    }
  }
);
