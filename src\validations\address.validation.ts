import { z } from 'zod';

export const addressSchema = z.object({
  clinic_name: z.string({ required_error: 'Clinic name is required' }),
  street_address: z.string({ required_error: 'Street address is required' }),
  city: z.string({ required_error: 'City is required' }),
  postal_code: z.string().optional(),
  phone_number: z.string().optional(),
  address_type: z.enum(['bill_to', 'ship_to'], { required_error: 'Address type is required' })
});

export type AddressInput = z.infer<typeof addressSchema>;