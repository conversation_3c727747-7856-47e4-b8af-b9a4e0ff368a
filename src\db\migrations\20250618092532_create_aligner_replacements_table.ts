import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("aligner_replacements", (table) => {
    table.increments("id").primary();
    table
      .integer("patient_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE");

    // Free-form note about the replacement
    table.string("replacement").notNullable();

    table.timestamps(true, true); // adds created_at & updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTableIfExists("aligner_replacements");
}
