import { Request, Response, NextFunction } from "express";
import { JwtPayload } from "jsonwebtoken";
import { verifyToken } from "../utils/services/jwt";
import db from "../config/db";
import { TABLE } from "../utils/Database/table";
import { sendResponse } from "../utils/helperFunctions/responseHelper";
import { UserRole } from "../utils/enums/users.enum";
import { appLogger, errorLogger } from "../config/logger";

export const authMiddleware = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    const startTime = Date.now();
    const ip = req.headers['x-forwarded-for'] as string || req.ip || 'unknown';
    
    try {
      const token = req.header("Authorization")?.replace("Bearer ", "");

      if (!token) {
        appLogger.warn('Authentication failed - No token provided', {
          endpoint: req.originalUrl,
          method: req.method,
          ip,
          userAgent: req.get('User-Agent')
        });
        sendResponse(res, 401, "Unauthorized! please login first.", false);
        return;
      }

      const decoded = verifyToken(token);
      const userId = (decoded as JwtPayload).id;

      // Fetch user details with role information
      const user = await db(`${TABLE.USERS} as u`)
        .leftJoin(`${TABLE.ROLES} as r`, 'u.role_id', 'r.id')
        .select('u.*', 'r.role_name as role')
        .where('u.id', userId)
        .first();

      if (!user) {
        appLogger.warn('Authentication failed - User not found', {
          userId,
          endpoint: req.originalUrl,
          method: req.method,
          ip,
          userAgent: req.get('User-Agent')
        });
        sendResponse(res, 404, "Account not found", false);
        return;
      }

      if (!user.is_verified) {
        appLogger.warn('Authentication failed - Account not verified', {
          userId: user.id,
          email: user.email,
          endpoint: req.originalUrl,
          method: req.method,
          ip
        });
        sendResponse(res, 403, "Your account is not verified", false);
        return;
      }

      if (!user.is_active) {
        appLogger.warn('Authentication failed - Account disabled', {
          userId: user.id,
          email: user.email,
          endpoint: req.originalUrl,
          method: req.method,
          ip
        });
        sendResponse(res, 403, "Your account has been disabled by the administrator", false);
        return;
      }

      // Log successful authentication
      appLogger.info('Authentication successful', {
        userId: user.id,
        email: user.email,
        role: user.role,
        endpoint: req.originalUrl,
        method: req.method,
        ip,
        authTime: Date.now() - startTime
      });

      req.user = user;
      next();
    } catch (error: any) {
      errorLogger.error('JWT verification error', {
        error: error.message,
        stack: error.stack,
        endpoint: req.originalUrl,
        method: req.method,
        ip,
        userAgent: req.get('User-Agent')
      });
      sendResponse(res, 401, "Unauthorized! please login first.", false);
      return;
    }
  };
