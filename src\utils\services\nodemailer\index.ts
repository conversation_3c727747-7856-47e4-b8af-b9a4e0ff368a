import dotenv from "dotenv";
dotenv.config();

interface SendEmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  senderName?: string;
  senderEmail?: string;
}

export async function sendEmail({
  to,
  subject,
  html,
  senderName = "Orthodontic Team",
  senderEmail = process.env.BREVO_VERIFICATION_EMAIL || "<EMAIL>",
}: SendEmailOptions) {
  const payload = {
    sender: { name: senderName, email: senderEmail },
    to: Array.isArray(to) ? to.map(email => ({ email })) : [{ email: to }],
    subject,
    htmlContent: html,
  };

  try {
    const response = await fetch("https://api.brevo.com/v3/smtp/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": process.env.BREVO_API_KEY || "",
      } as Record<string, string>,
      body: JSON.stringify(payload),
    });
    const data = (await response.json()) as any;
    if (data.messageId) {
      console.log("✅ Brevo email sent:", data.messageId);
      return data;
    } else {
      console.error("❌ Brevo send failed:", data);
      throw new Error("Brevo API failed");
    }
  } catch (error) {
    console.error("❌ Brevo API error:", error);
    throw error;
  }
}
