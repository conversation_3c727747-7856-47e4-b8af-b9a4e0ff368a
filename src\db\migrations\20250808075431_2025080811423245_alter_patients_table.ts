import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable("patients", (table) => {
    table.integer("sets").nullable();
    table.text("archRequired").nullable();
    table.text("notes").nullable(); // Stores free text
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable("patients", (table) => {
    table.dropColumn("sets");
    table.dropColumn("archRequired");
    table.dropColumn("notes");
  });
}
