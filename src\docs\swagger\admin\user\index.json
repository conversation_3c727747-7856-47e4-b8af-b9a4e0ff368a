{"paths": {"/auth/register-user": {"post": {"summary": "Create a new user (admin only)", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe", "description": "Optional. Must be unique if provided."}, "role_id": {"type": "string", "example": "2", "description": "Role ID from roles table."}}, "required": ["first_name", "last_name", "email", "role_id"]}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": ["string", "null"], "example": "johndoe"}, "role_id": {"type": "integer", "example": 2}, "role": {"type": "string", "example": "DOCTOR"}, "is_active": {"type": "boolean", "example": true}, "is_verified": {"type": "boolean", "example": true}}}}}}}}, "400": {"description": "Validation error, email or username already exists"}, "403": {"description": "Only admins can create new users"}, "500": {"description": "Internal server error"}}}}, "/user": {"get": {"summary": "Get all users", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "example": 1}, "description": "Page number"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "example": 10}, "description": "Number of items per page"}], "responses": {"200": {"description": "Users fetched successfully"}, "500": {"description": "Internal server error"}}}}, "/user/{id}": {"patch": {"summary": "Update user by ID", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the user to update"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "profile_image": {"type": "string", "format": "binary", "description": "User's profile image"}}}}}}, "responses": {"200": {"description": "User updated successfully"}, "400": {"description": "Validation error or email already exists"}, "500": {"description": "Internal server error"}}}, "delete": {"summary": "Delete user by ID", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the user to delete"}], "responses": {"200": {"description": "User deleted successfully"}, "400": {"description": "User not found"}, "500": {"description": "Internal server error"}}}, "get": {"summary": "Get user by ID", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "ID of the user to retrieve"}], "responses": {"200": {"description": "User fetched successfully"}, "400": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/user/doctors": {"get": {"summary": "Get all doctors", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Doctors fetched successfully"}, "500": {"description": "Internal server error"}}}}, "/user/patients": {"get": {"summary": "Get all patients with their doctors", "tags": ["Admin User Management"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Patients with doctors fetched successfully"}, "500": {"description": "Internal server error"}}}}}}