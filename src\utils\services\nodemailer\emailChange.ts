import { sendEmail } from "./index";
import path from "path";
import fs from "fs";
import handlebars from "handlebars";

export const sendEmailChangeVerification = async ({
  currentEmail,
  newEmail,
  token,
  name,
}: {
  currentEmail: string;
  newEmail: string;
  token: string;
  name: string;
}) => {
  try {
    const templatePath = path.join(
      __dirname,
      "templates/emailChangeVerification.hbs"
    );
    const templateSource = fs.readFileSync(templatePath, "utf-8");
    const template = handlebars.compile(templateSource);

    // Use token only — no email in query
    const verifyLink = `${process.env.BASE_URL}/api/v1/auth/verify-email?token=${token}`;

    const html = template({
      name,
      currentEmail,
      newEmail,
      verifyLink,
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
    });

    await sendEmail({
      to: currentEmail,
      subject: "Verify Your Email Change",
      html,
      senderName: process.env.EMAIL_FROM_NAME || "Orthodontic Lab Team",
      senderEmail: process.env.BREVO_VERIFICATION_EMAIL || process.env.EMAIL_FROM_ADDRESS || "<EMAIL>",
    });
  } catch (error) {
    console.error("Error sending email change verification:", error);
    throw error;
  }
};
