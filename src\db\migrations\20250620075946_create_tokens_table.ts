import { Knex } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(TABLE.TOKENS, (table) => {
    table.increments("id").primary();
    table
      .integer("user_id")
      .unsigned()
      .references("id")
      .inTable(TABLE.USERS)
      .onDelete("SET NULL");
    table.string("token").notNullable();
    table.timestamp("date").defaultTo(knex.fn.now());
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable(TABLE.TOKENS);
}
